# Created by .ignore support plugin (hsz.mobi)
### Python template
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
*.manifest
*.spec
*.cover
*.py,cover
*.mo
*.pot
logs
*.log
*.sage.py
*.stackdump
__pypackages__/
cython_debug/
.roo

# Folder config file
[Dd]esktop.ini

# Recycle Bin used on file shares
$RECYCLE.BIN/

help-api/lib/
help-api/lib64/
help-api/parts/
help-api/sdist/
help-api/var/
help-api/wheels/
help-api/pip-wheel-metadata/
help-api/share/python-wheels/
help-api/pip-log.txt
help-api/pip-delete-this-directory.txt
help-api/htmlcov/
help-api/.tox/
help-api/.nox/
help-api/.coverage
help-api/.coverage.*
help-api/.cache
help-api/nosetests.xml
help-api/coverage.xml
help-api/.hypothesis/
help-api/.pytest_cache/
help-api/cover/
help-api/local_settings.py
help-api/db.sqlite3
help-api/db.sqlite3-journal
help-api/instance/
help-api/.webassets-cache
help-api/.scrapy
help-api/docs/_build/
help-api/.pybuilder/
help-api/target/
help-api/.ipynb_checkpoints
help-api/profile_default/
help-api/ipython_config.py
help-api/celerybeat-schedule
help-api/celerybeat.pid
# help-api/.env
# help-api/.env.test
# help-api/env/
# help-api/ENV/
# help-api/env.bak/
help-api/.venv
help-api/venv/
help-api/venv.bak/
help-api/.spyderproject
help-api/.spyproject
help-api/.ropeproject
help-api//site
help-api/.mypy_cache/
help-api/.dmypy.json
help-api/dmypy.json
help-api/.pyre/
help-api/.pytype/
help-api/Thumbs.db
help-api/Thumbs.db:encryptable
help-api/ehthumbs.db
help-api/ehthumbs_vista.db
help-api/*.cab
help-api/*.msi
help-api/*.msix
help-api/*.msm
help-api/*.msp
help-api/*.lnk
help-api/.fuse_hidden*
help-api/.directory
help-api/.Trash-*
help-api/.nfs*
help-api/.DS_Store
help-api/.AppleDouble
help-api/.LSOverride
help-api/Icon
help-api/.DocumentRevisions-V100
help-api/.fseventsd
help-api/.Spotlight-V100
help-api/.TemporaryItems
help-api/.Trashes
help-api/.VolumeIcon.icns
help-api/.com.apple.timemachine.donotpresent
help-api/.AppleDB
help-api/.AppleDesktop
help-api/.apdisk
help-api/temp/
help-api/static/

help-web/npm-debug.log*
help-web/yarn-debug.log*
help-web/yarn-error.log*
help-web/pnpm-debug.log*
help-web/lerna-debug.log*
help-web/node_modules
help-web/.DS_Store
help-web/dist
help-web/dist-ssr
help-web/coverage
help-web/*.local

# unplugin-auto-import
help-web/auto-imports.d.ts
help-web/components.d.ts
help-web/.eslintrc-auto-import.json
help-web/stats.html

# Editor directories and files
help-web/.idea
help-web/*.suo
help-web/*.ntvs*
help-web/*.njsproj
help-web/*.sln
help-web/*.sw?