MODE = "test"

# mongo 数据库名字
MONGO_DB_NAME="oms_test_db"

# MongoDB 用户名
MONGO_USERNAME="root"

# MongoDB 密码
MONGO_PASSWORD="builderx"

# MongoDB 主机地址
MONGO_HOST="localhost"

# MongoDB 端口
MONGO_PORT=27017


# Redis 库序号
REDIS_DB = 7

# Redis 任务队列名称
REDIS_TASK_QUEUE = "builderx-tasks"


# InfluxDB 用户名
INFLUXDB_USERNAME="root"

# InfluxDB 密码
INFLUXDB_PASSWORD="builderx"

# InfluxDB 组织名称
INFLUXDB_ORG="builderx"

# 存储桶名称
INFLUXDB_BUCKET="oms"

# 存储桶默认保存时间
INFLUXDB_RETENTION=30

# InfluxDB Token
INFLUXDB_ADMIN_TOKEN="c4363ac14b064b27c2263280893be4fd"