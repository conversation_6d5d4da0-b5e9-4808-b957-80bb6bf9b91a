FROM hub.apps.builderx.com/python:3.10.16-alpine

ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

RUN addgroup -S builderx && adduser -S builderx -G builderx

WORKDIR /app

# 复制后端代码
COPY . .

# 创建静态文件目录
RUN mkdir -p static

# 安装依赖
RUN pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple && pip cache purge

USER builderx

EXPOSE 8800

CMD ["fastapi", "run", "--workers", "4", "main.py"]