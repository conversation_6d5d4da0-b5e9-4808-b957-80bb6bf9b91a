from contextlib import asynccontextmanager

from fastapi import <PERSON>AP<PERSON>
from fastapi.staticfiles import StaticFiles
from starlette.middleware.cors import CORSMiddleware

from apps.config import get_settings
from apps.utils.wsbroadcast import WBroadCast
from apps.db import RedisDB, MongoDB, MongoUtil
from apps.services.internal import InitDataBase
from apps.services.audit import AuditMiddleware
from apps.common.global_exc import configure_exception
from apps.router import configure_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用程序生命周期 开始&关闭"""
    app.state.rdb = RedisDB
    app.state.mdb = MongoDB
    await MongoUtil.init_index()
    await InitDataBase().init_data()
    await WBroadCast.connect()
    yield
    await RedisDB.close()
    await WBroadCast.disconnect()
    MongoUtil.close()


# def configure_http_middleware(app: FastAPI):
#     """配置HTTP中间件"""

#     @app.middleware("http")
#     async def after_request(request: Request, call_next):
#         start_time = time.perf_counter()
#         resp: Response = await call_next(request)
#         process_time = time.perf_counter() - start_time
#         resp.headers["X-Process-Time"] = str(process_time)
#         await record_action(request, resp)
#         return resp


def configure_static(app: FastAPI):
    """配置静态资源"""
    pass


def configure_cors(app: FastAPI):
    """配置跨域"""
    if get_settings().mode == "prod":
        return
    origins = [
        "http://localhost",
        "http://localhost:3000",
        "http://localhost:3001",
    ]
    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    # 挂载静态文件目录
    # app.mount("/", StaticFiles(directory="static", html=True), name="static")


def create_app() -> FastAPI:
    """创建FastAPI后台应用,并初始化"""
    app = FastAPI(lifespan=lifespan)
    app.add_middleware(AuditMiddleware)
    configure_exception(app)
    configure_cors(app)
    # configure_http_middleware(app)
    configure_router(app)
    configure_static(app)
    return app
