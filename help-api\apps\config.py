import os
import re
from typing import Tuple
from functools import lru_cache

from dotenv import load_dotenv
from pydantic_settings import BaseSettings


__all__ = ["get_settings", "Settings"]

ENV_FILES = (".env", ".env.prod")
ROOT_PATH = os.path.dirname(os.path.abspath(os.path.join(__file__, "..")))


class DBSettings(BaseSettings):
    """数据库配置"""

    # """Redis配置"""
    redis_host: str = "localhost"
    redis_password: str = ""
    redis_port: int = 6379
    redis_db: int = 5
    rdb_prefix: str = "help:"  # Redis键前缀

    # mongo 配置
    mongo_db_name: str = "builderx-help"
    mongo_username: str = "root"
    mongo_password: str = "builderx"
    mongo_host: str = "**************"
    mongo_port: int = 27017

    # influx 配置
    influxdb_username: str = "root"
    influxdb_password: str = "builderx"
    influxdb_org: str = "builderx"
    influxdb_bucket: str = "oms"
    influxdb_admin_token: str = ""
    influxdb_host: str = "localhost"
    influxdb_port: int = 8086


class AppSettings(DBSettings):
    """应用配置
    server目录为后端项目根目录, 在该目录下创建 ".env" 文件, 写入环境变量(默认大写)会自动加载, 并覆盖同名配置(小写)
        eg.
        .env 文件内写入:
            UPLOAD_DIRECTORY='/tmp/test/'
            REDIS_URL='redis://localhost:6379'
            DATABASE_URL='mysql+pymysql://root:root@localhost:3306/likeadmin?charset=utf8mb4'
            上述环境变量会覆盖 upload_directory 和 redis_url
    """

    # 模式
    mode: str = "prod"  # dev, test, prod

    # 全局配置
    root_path: str = ROOT_PATH  # 项目根路径
    request_timeout: int = 15  # 默认请求超时
    timezone: str = "Asia/Shanghai"  # 时区
    datetime_fmt: str = "%Y-%m-%d %H:%M:%S"  # 日期时间格式
    secret: str = "XrEdLiUb"  # 系统加密字符
    domain: str = "http://127.0.0.1:8000"  # 当前域名

    video_directory: str = "/data/video"  # 视频存放目录
    upload_directory: str = "../../upload"  # 上传文件存放目录

    # 免登录接口
    not_auth_uri: Tuple = (
        # 用户登录和认证
        re.compile(r"user:(login|auth)"),
        # 视频数据和封面数据查看，下载，包含从车辆接口进入和从数据管理接口进入
        re.compile(r"data_manage:media:video:(cover|play|download):[a-z0-9]+\.(mp4|jpg)"),
        re.compile(r"vehicles:[0-9a-fA-F]{24}:service:video:(cover|play|download):[a-z0-9]+\.(mp4|jpg)"),
    )


@lru_cache()
def get_settings() -> AppSettings:
    """获取并缓存应用配置"""
    run_mode = os.getenv("OMS_MODE")
    if run_mode == "prod":
        load_dotenv(os.path.join(ROOT_PATH, ".env.prod"))
        return AppSettings(mode=run_mode)
    if run_mode == "test":
        load_dotenv(os.path.join(ROOT_PATH, ".env.test"))
        return AppSettings(mode=run_mode)
    # 默认 dev 模式，读取 server 目录下的配置
    for f in ENV_FILES:
        load_dotenv(dotenv_path=os.path.join(ROOT_PATH, f))
    return AppSettings()


Settings = get_settings()
