from typing import Union, Set, Optional, Awaitable

from redis import asyncio as aioredis

from apps.utils import singleton
from apps.config import get_settings


__all__ = ["RedisDB"]


@singleton
class RedisUtilClass:
    """Redis操作工具类"""

    def __init__(self, _=None) -> None:
        _c = get_settings()
        self.prefix: str = _c.rdb_prefix  # key通用前缀
        self.rdb: aioredis.Redis = aioredis.Redis(
            host=_c.redis_host,
            port=_c.redis_port,
            db=_c.redis_db,
            password=_c.redis_password,
            encoding="utf-8",
            decode_responses=True,
        )

    async def info(self, section: Optional[str] = None) -> dict:
        """Redis服务信息"""
        return await self.rdb.info(section)

    async def dbsize(self) -> dict:
        """当前数据库key数量"""
        return await self.rdb.dbsize()

    def gen_key(self, key: str) -> str:
        """获取key"""
        return f"{self.prefix}{key}"

    async def set(self, key: str, value: Union[str, int, float], timeout: Union[int, None] = None):
        """设置键值对"""
        await self.rdb.set(self.gen_key(key), value=value, ex=timeout)

    async def setnx(self, key: str, value: Union[str, int, float], timeout: Union[int, None] = None):
        """设置键值对, 如果key不存在"""
        return await self.rdb.set(self.gen_key(key), value=value, ex=timeout, nx=True)

    async def incr(self, key: str, amount: int = 1) -> int:
        """自增"""
        return await self.rdb.incr(self.gen_key(key), amount)

    async def get(self, key: str) -> Union[str, None]:
        """获取key的值"""
        return await self.rdb.get(self.gen_key(key))

    async def exist(self, key: str) -> bool:
        """判断多项key是否存在
        返回存在的key数量
        """
        if await self.rdb.exists(self.gen_key(key)) == 1:
            return True
        return False

    async def sset(self, key: str, *values: Union[str, int, float]):
        """将数据放入set缓存
        返回添加的数量
        """
        res = self.rdb.sadd(self.gen_key(key), *values)
        assert isinstance(res, Awaitable)
        return await res

    async def sget(self, key: str) -> Set:
        """根据 key 获取 set 中的所有值"""
        res = self.rdb.smembers(self.gen_key(key))
        assert isinstance(res, Awaitable)
        return await res

    async def srem(self, key: str, *values: Union[str, int, float]) -> int:
        """根据 key 删除有序集合中的值"""
        res = self.rdb.srem(self.gen_key(key), *values)
        assert isinstance(res, Awaitable)
        return await res

    async def zadd(self, key: str, mapping: dict) -> int:
        """将数据放入有序集合
        返回添加的数量
        """
        return await self.rdb.zadd(self.gen_key(key), mapping)

    async def zget(self, key: str, begin_index=0, end_index=-1) -> Set:
        """根据 key 获取有序集合中的所有值"""
        return await self.rdb.zrange(self.gen_key(key), begin_index, end_index)

    async def zkeys(self, key: str, start: int, end: int) -> Set:
        """根据 key 获取有序集合中的所有值"""
        return await self.rdb.zrange(self.gen_key(key), start, end)

    async def zrem(self, key: str, *values: Union[str, int, float]) -> int:
        """根据 key 删除有序集合中的值"""
        return await self.rdb.zrem(self.gen_key(key), *values)

    async def zkindex(self, key: str, value: Union[str, int, float]) -> int:
        """根据 key 获取有序集合中值的索引"""
        return await self.rdb.zrank(self.gen_key(key), value)

    async def hmset(self, key: str, mapping: dict) -> str:
        """设置key, 通过字典的方式设置多个field, value对
        返回添加的数量
        """
        res = self.rdb.hmset(self.gen_key(key), mapping=mapping)
        assert isinstance(res, Awaitable)
        return await res

    async def hset(self, key: str, field: str, value: Union[str, int, float]) -> str:
        """
        向hash表中放入数据,如果不存在将创建
        返回添加的数量
        """
        res = self.hmset(key, mapping={field: value})
        assert isinstance(res, Awaitable)
        return await res

    async def hgetall(self, key: str) -> dict:
        """获取key中所有的field和value"""
        res = self.rdb.hgetall(self.gen_key(key))
        assert isinstance(res, Awaitable)
        return await res

    async def hget(self, key: str, field: str) -> Optional[str]:
        """获取key中field域的值"""
        res = self.rdb.hget(self.gen_key(key), field)
        assert isinstance(res, Awaitable)
        return await res

    async def hexists(self, key: str, field: str) -> bool:
        """判断key中有没有field域名"""
        res = self.rdb.hexists(self.gen_key(key), field)
        assert isinstance(res, Awaitable)
        return await res

    async def hdel(self, key: str, *fields: str) -> int:
        """删除hash表中的值
        返回删除的数量
        """
        res = self.rdb.hdel(self.gen_key(key), *fields)
        assert isinstance(res, Awaitable)
        return await res

    async def ttl(self, key: str) -> int:
        """根据key获取过期时间"""
        return await self.rdb.ttl(self.gen_key(key))

    async def expire(self, key: str, time: int):
        """指定缓存失效时间"""
        await self.rdb.expire(self.gen_key(key), time)

    async def delete(self, *keys: str):
        """删除一个或多个键"""
        return await self.rdb.delete(*(self.gen_key(key) for key in keys))

    async def xadd(self, task_queue, message_data) -> str:
        return await self.rdb.xadd(self.gen_key(task_queue), message_data)

    async def lpush(self, key, value) -> int:
        res = self.rdb.lpush(self.gen_key(key), value)
        assert isinstance(res, Awaitable)
        return await res

    async def close(self):
        """关闭连接"""
        await self.rdb.aclose()


RedisDB = RedisUtilClass()
