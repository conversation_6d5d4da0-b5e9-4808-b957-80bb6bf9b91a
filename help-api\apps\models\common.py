import json
from typing import Annotated, Any, Callable
from enum import IntEnum

from bson import ObjectId
from pydantic import BaseModel, Field, ValidationError
from pydantic_core import core_schema


class _ObjectIdPydanticAnnotation:
    # Based on https://docs.pydantic.dev/latest/usage/types/custom/#handling-third-party-types.

    @classmethod
    def __get_pydantic_core_schema__(
        cls,
        _source_type: Any,
        _handler: Callable[[Any], core_schema.CoreSchema],
    ) -> core_schema.CoreSchema:
        def validate_from_str(input_value: str) -> ObjectId:
            try:
                return ObjectId(input_value)
            except Exception:
                raise ValidationError.from_exception_data(
                    "ObjectId",
                    [
                        {
                            "loc": ("id",),
                            "input": input_value,
                            "type": "value_error",
                            "ctx": {"error": "invalid ObjectId"},
                        }
                    ],
                )

        return core_schema.union_schema(
            [
                # check if it's an instance first before doing any further work
                core_schema.is_instance_schema(ObjectId),
                core_schema.no_info_plain_validator_function(validate_from_str),
            ],
            serialization=core_schema.to_string_ser_schema(),
        )


ObjectIdStr = Annotated[ObjectId, _ObjectIdPydanticAnnotation]
"""
ObjectIdStr
    自定义的 校验类型
    1. 会将符合条件的字符串转换为 bson.ObjectId 类型
    2. 可以处理 Pydantic 模型中 bson.ObjectId 无法兼容情况。
"""


class JSONObjectID(json.JSONEncoder):
    def default(self, o):
        if isinstance(o, ObjectId):
            return str(o)
        return json.JSONEncoder.default(self, o)


class PagingModel(BaseModel):
    """分页模型"""

    page_no: int = Field(default=1, ge=1)
    page_size: int = Field(default=10, ge=10, le=100)


class SortFlag(IntEnum):
    """排序标志"""

    ASC = 1
    DESC = -1
