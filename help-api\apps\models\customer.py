from typing import Union, List, Optional
from datetime import datetime
from enum import IntEnum

from pydantic import BaseModel, EmailStr, constr, Field

from .common import ObjectIdStr, PagingModel


# 操作部分接口
class OperationCreate(BaseModel):
    content: str
    sort: int
    description: str = ""
    answer_type: int = 1  # 1.软件 2.硬件
    device_type: int = 1  # 1.操作台 2.机械端 3.操作台和机械端都有
    is_need_tools: bool = False  # 是否需要工具
    is_need_detail: str = ""  # 需要的工具


class OperationOut(OperationCreate):
    id: str = Field(..., alias="_id")
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None


class OperationQuery(PagingModel):
    keyword: Optional[str] = None


# 现象部分接口
class PhenomenonCreate(BaseModel):
    pid: str
    sort: int
    content: str
    description: str = ""
    operation_id: Optional[List] = []


class PhenomenonOut(PhenomenonCreate):
    id: str = Field(alias="_id")
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    children: Optional[List] = []


class PhenomenonUpdate(BaseModel):
    id: str
    pid: Optional[str] = None
    content: Optional[str] = None
    description: Optional[str] = None
    operation_id: Optional[List] = []
    sort: Optional[int] = None


class ConsumerCreate(BaseModel):
    name: str
    phone: str
    devices_control: Optional[List] = []
    devices_vehicle: Optional[List] = []


class ConsumerOut(ConsumerCreate):
    id: str = Field(..., alias="_id")
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None


class OrderQuery(PagingModel):
    status: Optional[int] = None  # 1: "待处理"  2: "待反馈"  3: "已解决"  4: "拓疆者处理中"  5: "未解决"
    keyword: Optional[str] = None


class OrderCreate(BaseModel):
    name_id: str
    name: str
    phone: str
    device_control_name: str
    device_vehicle_name: str
    status: int = 1  # 1: "未解决"  2: "待反馈"  3: "已解决"  4: "拓疆者处理中"
    remark: str = ""
    phenomenon_description: str = ""
    phenomenon_id: Optional[List] = []
    operation_id: Optional[List] = []
    operation_checked: Optional[List] = []
    timeline: Optional[List] = []
    resolve: Optional[Union[int, str]] = None


class OrderUpdate(OrderCreate):
    timeline_content: Optional[str] = None


class OrderOut(OrderCreate):
    id: str = Field(..., alias="_id")
    create_time: Optional[datetime] = None
    completion_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    create_username: str = ""
