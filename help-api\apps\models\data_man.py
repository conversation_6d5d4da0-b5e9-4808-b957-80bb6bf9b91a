import os
from datetime import datetime
from typing import Optional, List

import arrow
from pydantic import BaseModel, Field

from .common import PagingModel, ObjectIdStr, SortFlag
from apps.config import get_settings


class TimeRangeQuery(BaseModel):
    """时间段条件查询"""

    start_time: datetime
    end_time: datetime


class RecordVideoTask(BaseModel):
    """单个车辆的视频录制任务"""

    vehicle_id: str
    rtsp_uri_list: List[str] = []
    segment_time: int = 300
    save_days: int = 30


class RecordMqttTask(BaseModel):
    """单个车辆的 Mqtt 信令录制任务"""

    vehicle_id: str
    topic_name_list: list[str] = []


class VideoQuery(PagingModel):
    vehicle_id: Optional[ObjectIdStr] = None
    stream_name: Optional[str] = None
    time_order: SortFlag = SortFlag.DESC
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


class VideoSegment(BaseModel):
    vehicle_id: str
    stream_name: str
    segment_start_time: datetime
    path: str
    size: int


class VideoListOut(BaseModel):
    id: ObjectIdStr = Field(..., alias="_id")
    vehicle_id: ObjectIdStr
    stream_name: str
    segment_start_time: datetime
    size: int

    @property
    def path(self) -> str:
        dt = arrow.get(self.segment_start_time, tzinfo="UTC")
        file_name = dt.to(get_settings().timezone).format("YYYY-MM-DD_HH-mm-ss")
        _p = os.path.join(
            get_settings().video_directory,
            str(self.vehicle_id),
            self.stream_name,
            f"{file_name}.mp4",
        )
        return _p

    @property
    def cover_path(self) -> str:
        dt = arrow.get(self.segment_start_time, tzinfo="UTC")
        file_name = dt.to(get_settings().timezone).format("YYYY-MM-DD_HH-mm-ss")
        return os.path.join(
            get_settings().video_directory,
            str(self.vehicle_id),
            self.stream_name,
            "cover",
            f"{file_name}.jpg",
        )

    @property
    def exists(self) -> bool:
        return os.path.exists(self.path)

    @property
    def cover_exists(self) -> bool:
        return os.path.exists(self.cover_path)

    def iterfile(self, start: int, end: int):
        with open(self.path, mode="rb") as file:
            file.seek(start)
            while start <= end:
                bytes_to_read = min(1024 * 1024, end - start + 1)
                data = file.read(bytes_to_read)
                if not data:
                    break
                start += len(data)
                yield data
