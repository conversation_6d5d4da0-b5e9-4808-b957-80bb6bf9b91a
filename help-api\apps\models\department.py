from typing import List, Optional
from datetime import datetime
from enum import IntEnum

from pydantic import BaseModel, Field

from .common import PagingModel, ObjectIdStr


class DepartmentStatus(IntEnum):
    """部门状态"""
    NORMAL = 0  # 正常
    DISABLED = 1  # 禁用


class DepartmentCreate(BaseModel):
    """部门创建校验模型"""
    name: str = Field(..., min_length=2, max_length=50)
    parent_id: Optional[ObjectIdStr] = None  # 父部门ID，如果为None则为顶级部门
    is_enable: int = 0  # 0-启用，1-禁用
    remark: str = ""  # 备注
    sort: int = 100  # 排序


class DepartmentOut(DepartmentCreate):
    """部门输出模型"""
    id: ObjectIdStr = Field(..., alias="_id")
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    children: Optional[List] = []  # 子部门列表
    level: int = 1  # 部门层级，从1开始


class DepartmentUpdate(BaseModel):
    """部门更新校验模型"""
    name: Optional[str] = None
    parent_id: Optional[ObjectIdStr] = None
    is_enable: Optional[int] = None
    remark: Optional[str] = None
    sort: Optional[int] = None
    level: Optional[int] = None


class DepartmentQuery(PagingModel):
    """部门查询模型"""
    name: Optional[str] = None  # 部门名称，模糊查询
    is_enable: Optional[int] = None  # 状态