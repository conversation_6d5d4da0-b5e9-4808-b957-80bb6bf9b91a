from enum import Enum
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, field_serializer

from .common import PagingModel


class DeviceStatus(int, Enum):
    UNREGISTERED = 0  # 未注册
    REGISTERED = 1  # 已注册
    DISABLED = 2  # 禁用


class DeviceOnlineStatus(BaseModel):
    """设备在线状态模型"""

    online: bool = False
    online_time: Optional[datetime] = None
    offline_time: Optional[datetime] = None
    data_sync_status: int = 0

    @field_serializer("online_time", "offline_time")
    def serialize_dt(self, v: datetime, _info):
        if v is None:
            return None
        return v.strftime("%Y-%m-%dAS%H:%M:%S")


class DeviceQuery(PagingModel):
    """设备查询模型"""

    name: Optional[str] = None
    device_id: Optional[str] = None
    device_name: Optional[str] = None
    status: DeviceStatus = DeviceStatus.REGISTERED


class DeviceUpdate(BaseModel):
    """设备更新模型"""

    device_name: Optional[str]
    status: DeviceStatus = DeviceStatus.REGISTERED


class UnregisteredDeviceInfo(BaseModel):
    """未注册设备信息"""

    device_id: str
    device_name: str = ""
    ip: str
    local_ip: str = ""
    uptime: int = 0
    online_time: Optional[datetime] = None
