"""
    what is OPC?
        OPC is operation console
"""

from typing import Optional, Union, List
from pydantic import BaseModel, Field

from .common import PagingModel, ObjectIdStr


class Query(PagingModel):
    """操作台查询"""

    name: Optional[str] = None  # 操作台名称
    device_id: Optional[str] = None  # 设备 ID


class Add(BaseModel):
    """增加操作台"""

    name: str
    vehicle_type: List[int] = []
    organization: List[str] = ["builderx"]
    tags: List[str] = []


class Detail(BaseModel):
    """操作台列表返回信息"""

    id: ObjectIdStr = Field(alias="_id")
    name: str
    vehicle_type: List[int] = []
    organization: List[str] = []
    tags: List[str] = []


class Update(BaseModel):
    """更新操作台信息"""

    name: Optional[str] = None
    type: Optional[str] = None
    vehicle_type: List[int] = []
    organization: List[str] = []
    tags: List[str] = []


class Device4OPCModel(BaseModel):
    """设备更新修改为信任设备"""

    device_id: str = Field(..., min_length=10, max_length=24, description="device ID")


class AndroidControlParams(BaseModel):
    """C车辆模型"""

    vehicle_id: Union[str, ObjectIdStr]
    android: dict
    handle_config: dict
    restart_time: int


class AndroidControlParamsOut(AndroidControlParams):
    """C车辆模型"""

    online: bool = False
    is_lock: int = 2
