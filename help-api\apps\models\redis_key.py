""" redis key 生成类
    主要是为了统一 redis key 的生成规则，方便维护和更改
"""

from bson import ObjectId


class Menu:
    """菜单相关的 redis key"""

    prefix = "menu"
    max_menu_id = f"{prefix}:max_id"


class UserInfo:
    """用户信息相关的 redis key"""

    prefix = "user:info"

    def __init__(self, id_: str | ObjectId):
        self.id = str(id_) if isinstance(id_, ObjectId) else id_

    @property
    def info(self):
        """str 用户信息"""
        return f"{self.prefix}:{self.id}"

    @property
    def available_opc(self):
        """set 用户可操作的操作台集合"""
        return f"{self.prefix}:available_opconsoles"

    @property
    def available_vehicles(self):
        """set 用户可操作的车辆集合"""
        return f"{self.prefix}:available_vehicles"


class UserToken:
    """用户认证相关的 redis key"""

    prefix: str = "user:token"

    def __init__(self, id_: str | ObjectId):
        self.id = str(id_) if isinstance(id_, ObjectId) else id_

    @property
    def token(self):
        """hash 用户 token"""
        return f"{self.prefix}:{self.id}"


class OPCDevice:
    """安卓设备相关的 redis key"""

    un_register_set = "op_console:device:un_register"

    def __init__(self, id_: str):
        self.id = id_
        self.prefix = f"op_console:device:{id_}"

    @property
    def unbound(self):
        """设备未绑定此 key 有效"""
        return f"{self.prefix}:unbound"

    @property
    def status(self):
        """dict 设备状态"""
        return f"{self.prefix}:status"


class OPC:
    """操作台相关的 redis key"""

    def __init__(self, id_: str | ObjectId):
        self.id = str(id_) if isinstance(id_, ObjectId) else id_
        self.prefix = f"op_console:{self.id}"

    @property
    def available_vehicles(self):
        """set 操作台可操作的车辆集合"""
        return f"{self.prefix}:available_vehicles"

    @property
    def locked_vehicles(self):
        """set 操作台上锁定的车辆集合"""
        return f"{self.prefix}:locked_vehicles"

    @property
    def online(self):
        """str 设备在线状态"""
        return f"{self.prefix}:status:online"

    @property
    def online_time(self):
        """list 车辆上线时间"""
        return f"{self.prefix}:status:online_time"

    @property
    def offline_time(self):
        """list 车辆离线时间"""
        return f"{self.prefix}:status:offline_time"


class VehicleDevice:
    """米文设备相关的 redis key"""

    un_register_set = "vehicle:device:un_register"

    def __init__(self, id_: str | ObjectId):
        self.id = str(id_) if isinstance(id_, ObjectId) else id_
        self.prefix = f"vehicle:device:{id_}"

    @property
    def status(self):
        return f"{self.prefix}:status"


class Vehicle:
    """车辆相关的 redis key"""

    def __init__(self, id_: str | ObjectId):
        self.id = str(id_) if isinstance(id_, ObjectId) else id_
        self.prefix = f"vehicle:{id_}"

    @property
    def metadata(self) -> str:
        """str 当前车辆上锁定的操作台"""
        return f"{self.prefix}:metadata"

    @property
    def locked_op_console(self):
        """str 当前车辆上锁定的操作台"""
        return f"{self.prefix}:locked_op_console"

    @property
    def online_status(self):
        """int 车辆在线状态"""
        return f"{self.prefix}:status:online_status"

    @property
    def online_time(self):
        """list 车辆上线时间"""
        return f"{self.prefix}:status:online_time"

    @property
    def offline_time(self):
        """list 车辆离线时间"""
        return f"{self.prefix}:status:offline_time"

    @property
    def ros_nodes(self):
        """dict 车辆程序节点状态"""
        return f"{self.prefix}:status:ros_nodes"

    @property
    def params_sync_status(self):
        """str 车辆在线状态"""
        return f"{self.prefix}:status:params_sync_status"

    @property
    def other_status(self):
        """dict 其他状态字典"""
        return f"{self.prefix}:status:other"

    @property
    def room(self):
        """str 车辆所在房间"""
        return f"{self.prefix}:room"


class DataManager:
    """数据管理模块相关的 redis key"""

    __prefix = "data_manager:record_task"
    mqtt_monitor = f"{__prefix}:mqtt_monitor"
    video_monitor = f"{__prefix}:video_monitor"
