from typing import List, Optional
from datetime import datetime
from enum import IntEnum

from bson import ObjectId
from pydantic import BaseModel, EmailStr, Field

from .common import PagingModel, ObjectIdStr
from .permissions import FuncId, RoleResourceFlag


SUPER_ADMIN_ID = ObjectId("aaaaaaaaaaaaaaaaaaaaaaaa")
SUPER_ADMIN_ROLE_ID = ObjectId("aaaaaaaaaaaaaaaaaaaaaaaa")
DEFAULTS_ROLE_ID = ObjectId("bbbbbbbbbbbbbbbbbbbbbbbb")


class Status(IntEnum):
    """用户状态"""

    NORMAL = 0
    LOCKED = 1
    DELETED = 2


class BaseInfo(BaseModel):
    """用户基础信息"""

    id: ObjectIdStr = Field(..., alias="_id")
    role_ids: List[ObjectIdStr] = []
    username: str
    nickname: str
    email: EmailStr
    avatar: str = ""
    status: Status


class CacheInfo(BaseInfo):
    """
    用户缓存信息，在基础信息上增加了 is_super_admin 字段
    在 token 校验完成后将用户信息缓存到 request.state 中
    """

    is_super_admin: bool = False


class FullInfo(BaseModel):
    """用户完整信息，包含前端菜单信息"""

    user: BaseInfo
    permissions: List[str] = []


class Update(BaseModel):
    """用户信息修改"""

    id: ObjectIdStr
    role_ids: List[ObjectIdStr] = []
    username: str = ""
    nickname: str = ""
    email: EmailStr
    avatar: str = ""
    status: Status = Status.NORMAL


class Create(BaseModel):
    """用户创建校验模型"""

    username: str = Field(..., min_length=3, max_length=20)
    nickname: str = Field(..., min_length=2, max_length=20)
    password: str = Field(..., min_length=8, max_length=48)
    email: EmailStr
    role_ids: List[ObjectIdStr] = []
    avatar: str = ""


class Query(PagingModel):
    """用户模糊查询"""

    username: Optional[str] = None
    nickname: Optional[str] = None
    role: Optional[ObjectIdStr] = None


class ChangePass(BaseModel):
    id: ObjectIdStr
    old_password: str = Field(..., min_length=8, max_length=48)
    new_password: str = Field(..., min_length=8, max_length=48)


class ChangeStatus(BaseModel):
    id: ObjectIdStr
    status: Status


class Login(BaseModel):
    grant_type: Optional[str] = "password"
    username: str
    password: str = Field(..., min_length=8, max_length=48)


class RoleCreate(BaseModel):
    name: str
    remark: str = ""
    sort: int = 100


class RoleOut(RoleCreate):
    id: ObjectIdStr = Field(..., alias="_id")
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    menus: List[int] = []
    apis: List[FuncId] = []
    resource_flag: Optional[RoleResourceFlag] = None
    opconsoles: List[ObjectIdStr] = []
    vehicles: List[ObjectIdStr] = []
    is_disable: int = 0
    department_id: Optional[ObjectIdStr] = None  # 角色所属部门


class RoleUpdate(BaseModel):
    name: str
    remark: Optional[str] = None
    sort: int = 100
    is_disable: int = 0
    department_id: Optional[ObjectIdStr] = None  # 角色所属部门


class RoleUpdateMenus(BaseModel):
    """更新角色菜单"""

    menus: List[int] = []


class RoleUpdatePermission(BaseModel):
    """更新角色 API 权限"""

    apis: List[FuncId] = []


class RoleUpdateResource(BaseModel):
    """更新角色资源权限"""

    resource_flag: Optional[RoleResourceFlag] = None
    opconsoles: List[ObjectIdStr] = []
    vehicles: List[ObjectIdStr] = []


# 菜单部分接口
class MenuCreate(BaseModel):
    pid: int
    menu_type: str  # 权限类型: [M=目录, C=菜单, A=按钮]
    menu_name: str  # 菜单名称
    menu_icon: str
    menu_sort: int
    perms: str
    params: str
    paths: str
    component: str
    selected: str
    is_cache: int  # 是否缓存: [0=否, 1=是]
    is_show: int
    is_disable: int


class MenuOut(MenuCreate):
    id: int = Field(alias="_id")
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    children: Optional[List] = []


class MenuUpdate(BaseModel):
    id: int
    pid: Optional[int] = None
    menu_type: Optional[str] = None
    menu_name: Optional[str] = None
    menu_icon: Optional[str] = None
    menu_sort: Optional[int] = None
    perms: Optional[str] = None
    paths: Optional[str] = None
    component: Optional[str] = None
    selected: Optional[str] = None
    is_cache: Optional[int] = None
    is_show: Optional[int] = None
    is_disable: Optional[int] = None
