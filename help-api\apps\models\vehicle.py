"""
车辆相关模型
"""

from enum import Enum
from datetime import datetime
from typing import Optional, Dict, Any

import arrow
from pydantic import Field, field_validator
from pydantic import BaseModel

from .common import PagingModel, ObjectIdStr


class LockStatus(Enum):
    """车辆锁定状态"""

    UNLOCKED = 0
    MY_LOCKED = 1
    OTHER_LOCKED = 2


class MetaData(BaseModel):
    """车辆元数据"""

    key: str = Field(..., min_length=3, max_length=32)
    value: str
    type: str = ""
    desc: str = ""


class RealTimeStatus(BaseModel):
    is_online: bool = False
    is_locked: bool = False
    is_synced: bool = True
    locked_by_id: str = ""
    locked_by_name: str = ""


class BaseInfo(BaseModel):
    """车辆基本信息"""

    vehicle_name: str = Field(..., min_length=1, max_length=32)
    vehicle_type: Optional[int]
    description: str = Field("", min_length=0, max_length=128)


class Create(BaseInfo):
    """车辆创建"""

    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None
    devices: list = []


class Update(BaseInfo):
    """车辆更新"""

    pass


class Query(PagingModel):
    """车辆查询"""

    vehicle_name: Optional[str] = None
    vehicle_type: Optional[int] = None


class Descript(RealTimeStatus):
    """车辆简介数据"""

    id: ObjectIdStr = Field(alias="_id")
    vehicle_name: str
    vehicle_type: int
    organization: list = []
    tags: list = []
    create_time: Optional[datetime] = None
    update_time: Optional[datetime] = None


class ListOutApi(BaseModel):
    """车辆列表输出"""

    count: int = 0
    lists: list[Descript] = []


class Device4Vehicle(BaseModel):
    """车辆绑定设备模型"""

    device_id: str = Field(..., min_length=10, max_length=64, description="device ID")


class DeviceInfo(BaseModel):
    """设备信息"""

    id: str = Field(..., min_length=10, max_length=64, description="device ID")
    name: Optional[str] = None
    create_time: Optional[datetime] = arrow.utcnow().datetime


class CameraType(Enum):
    GMSL = 1
    IP = 2
    VIRTUAL = 2


class CAN(BaseModel):
    id: str
    bitrate: int
    ext_fd: str = ""
    is_enable: bool = False


class GMSLCamera(BaseModel):
    id: str
    camera_id: str
    camera_name: str
    type: str
    format: str = "UYVY"
    is_enable: bool = False


class IPCamera(BaseModel):
    id: str
    camera_id: str
    camera_name: str
    uri: str
    format: str = "h264"
    is_enable: bool = False


class CameraStatus(BaseModel):
    id: str
    camera_name: str
    camera_id: str
    is_enable: bool = False


class MixStreamCam(BaseModel):
    device: str
    detail: str = ""
    left: int = Field(..., ge=0)
    top: int = Field(..., ge=0)
    width: int = Field(..., ge=0)
    height: int = Field(..., ge=0)
    crop_left: int = Field(0, ge=0)
    crop_top: int = Field(0, ge=0)
    crop_width: int = Field(0, ge=0)
    crop_height: int = Field(0, ge=0)
    alpha: float = Field(1, ge=0, le=1)
    rotate: int = Field(0, ge=0, le=3)
    flip: int = Field(0, ge=0, le=3)
    z_index: int = Field(0, ge=0)
    primary: int = Field(0, ge=0, le=1)
    border_width: int = Field(0, ge=0, le=16)
    border_color: str = "#FF5F00"

    @field_validator("border_color")
    def check_color_code(cls, v):
        if not v.startswith("#") or len(v) != 7:
            raise ValueError("Color code must be a hex value, e.g., #FF5F00")
        try:
            int(v[1:], 16)
        except ValueError:
            raise ValueError("Color code must be a valid hex value")
        return v


class MixLayoutTemplate(BaseModel):
    name: str
    type: int = 0
    camera_list: dict[str, MixStreamCam]


class MixLayout(BaseModel):
    index: int = 0
    camera_list: dict[str, MixStreamCam]


class AndroidParams(BaseModel):
    """安卓参数"""

    android: Dict[str, Any] = {}
    handleConfig: Dict[str, Any] = Field({}, description="处理配置")
