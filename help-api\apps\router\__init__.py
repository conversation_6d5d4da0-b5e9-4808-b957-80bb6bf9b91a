import re

from fastapi import FastAPI, Depends

from apps.services.permissions import Access
from .internal_interface import router as internal_inface_router
from .metadata import router as metadata_router
from .user import router as user_router
from .vehicles import router as vehicle_router
from .websocket import router as websocket_router
from .customer import router as customer_manage_router
from .customer_jp import router as customer_manage_jp_router
from .material import router as material_router
from .opconsole import router as op_console_router
from .data_man import router as data_manage_router
from .devices_man import router as devices_router
from .guide_man import router as guide_router
from .project_man_jp import router as project_router
from .audit import router as audit_router


tags_metadata = [
    {"name": "元数据管理", "description": "全局元数据管理接口"},
    {"name": "车辆管理", "description": "车辆相关操作"},
    {"name": "车辆服务", "description": "车辆服务相关操作，业务侧接口"},
    {"name": "车辆参数", "description": "车辆参数相关操作，运维侧接口"},
    {"name": "操作台管理", "description": "遥控操作台相关接口"},
    {"name": "数据管理", "description": "获取车辆信令视频数据"},
    {"name": "设备管理", "description": "未信任设备的管理，包括安卓和米文"},
    {"name": "用户管理", "description": "用户相关"},
    {"name": "用户认证", "description": "用户认证相关，以后要合并到认证管理中"},
    {"name": "角色管理", "description": "用户角色相关接口，不同角色有不同权限"},
    {"name": "菜单管理", "description": "web界面菜单管理"},
    {"name": "审计日志", "description": "审计管理，查看用户操作日志"},
    {
        "name": "内部接口",
        "description": "定义了一些内部接口，比如初始化数据库、数据管理模块的链接，内部接口不应该暴露在外部访问",
    },
]


def slash_patcher(app: FastAPI):
    """修复路由尾部斜杠问题，兼容有没有斜杠两种情况"""
    app.router.redirect_slashes = False
    for r in app.routes:
        if r.path_regex.pattern.endswith("/$"):  # type: ignore
            r.path_regex = re.compile(r.path_regex.pattern.replace("/$", "/?$"))  # type: ignore
        else:
            r.path_regex = re.compile(r.path_regex.pattern.replace("$", "/?$"))  # type: ignore


def configure_router(app: FastAPI):
    """配置路由"""
    app.openapi_tags = tags_metadata
    # 内部接口，模块直接调用，不需要鉴权
    app.include_router(internal_inface_router, prefix="/internal")
    app.include_router(
        metadata_router,
        prefix="/api/v1",
        dependencies=[Depends(Access())],
    )
    app.include_router(
        user_router,
        prefix="/api/v1",
        dependencies=[Depends(Access())],
    )
    app.include_router(
        vehicle_router,
        prefix="/api/v1",
        dependencies=[Depends(Access())],
    )
    app.include_router(
        op_console_router,
        prefix="/api/v1",
        dependencies=[Depends(Access())],
    )
    app.include_router(
        data_manage_router,
        prefix="/api/v1",
        dependencies=[Depends(Access())],
    )
    app.include_router(
        devices_router,
        prefix="/api/v1",
        dependencies=[Depends(Access())],
    )
    app.include_router(
        audit_router,
        prefix="/api/v1",
        dependencies=[Depends(Access())],
    )
    app.include_router(customer_manage_router, prefix="/api/v1", dependencies=[Depends(Access())])
    app.include_router(customer_manage_jp_router, prefix="/api/v1", dependencies=[Depends(Access())])
    app.include_router(material_router, prefix="/api/v1")
    app.include_router(guide_router, prefix="/api/v1", dependencies=[Depends(Access())])
    app.include_router(project_router, prefix="/api/v1", dependencies=[Depends(Access())])

    # websocket接口验证具体实现在websocket service中实现
    app.include_router(websocket_router, prefix="/api/v1")

    slash_patcher(app)
