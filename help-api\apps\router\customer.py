from fastapi import APIRouter, Request, Query, Body, Depends
from fastapi.security import OAuth2PasswordRequestForm

from apps.common import unified_resp
from apps.models.common import PagingModel
from apps.services.customer import OperationService, PhenomenonService, ConsumerService, OrderService, AptLarkTable
import apps.models.customer as CustomerModel


router = APIRouter(prefix="/customer")

#################### 客服管理 ####################


@router.post("/operation", tags=["操作管理"])
@unified_resp
async def operation_add(_: Request, operation: CustomerModel.OperationCreate):
    operation_srv = OperationService()
    return await operation_srv.operation_create(operation)


@router.get("/operation", tags=["操作管理"])
@unified_resp
async def operation_list(_: Request, q: CustomerModel.OperationQuery = Depends()):
    operation_srv = OperationService()
    return await operation_srv.operation_list(q)


@router.put("/operation/{operation_id}", tags=["操作管理"])
@unified_resp
async def operation_update(
    _: Request,
    operation_id: str,
    operation: CustomerModel.OperationCreate,
):
    operation_srv = OperationService()
    return await operation_srv.operation_update(operation_id, operation)


@router.delete("/operation/{operation_id}", tags=["操作管理"])
@unified_resp
async def operation_delete(_: Request, operation_id: str):
    operation_srv = OperationService()
    return await operation_srv.operation_delete(operation_id)


#################### 现象管理 ####################

@router.post("/phenomenon", tags=["现象管理"])
@unified_resp
async def phenomenon_add(_: Request, phenomenon: CustomerModel.PhenomenonCreate):
    phenomenon_srv = PhenomenonService()
    return await phenomenon_srv.phenomenon_create(phenomenon)


@router.get("/phenomenon", tags=["现象管理"])
@unified_resp
async def phenomenon_list(_: Request):
    phenomenon_srv = PhenomenonService()
    return await phenomenon_srv.phenomenon_list()


@router.delete("/phenomenon/{phenomenon_id}", tags=["现象管理"])
@unified_resp
async def phenomenon_delete(_: Request, phenomenon_id: str):
    phenomenon_srv = PhenomenonService()
    return await phenomenon_srv.phenomenon_delete(phenomenon_id)


@router.put("/phenomenon/{phenomenon_id}", tags=["现象管理"])
@unified_resp
async def phenomenon_update(
    _: Request,
    phenomenon_id: str,
    phenomenon: CustomerModel.PhenomenonUpdate,
):
    phenomenon_srv = PhenomenonService()
    return await phenomenon_srv.phenomenon_update(phenomenon_id, phenomenon)



#################### 用户管理 ####################

@router.post("/consumer", tags=["用户管理"])
@unified_resp
async def consumer_add(_: Request, consumer: CustomerModel.ConsumerCreate):
    consumer_srv = ConsumerService()
    return await consumer_srv.consumer_create(consumer)


@router.get("/consumer", tags=["用户管理"])
@unified_resp
async def consumer_list(_: Request):
    consumer_srv = ConsumerService()
    return await consumer_srv.consumer_list()


@router.delete("/consumer/{consumer_id}", tags=["用户管理"])
@unified_resp
async def consumer_delete(_: Request, consumer_id: str):
    consumer_srv = ConsumerService()
    return await consumer_srv.consumer_delete(consumer_id)


@router.put("/consumer/{consumer_id}", tags=["用户管理"])
@unified_resp
async def consumer_update(
    _: Request,
    consumer_id: str,
    consumer: CustomerModel.ConsumerCreate,
):
    consumer_srv = ConsumerService()
    return await consumer_srv.consumer_update(consumer_id, consumer)


#################### 工单管理 ####################

@router.post("/order", tags=["工单管理"])
@unified_resp
async def order_add(_: Request, order: CustomerModel.OrderCreate):
    order_srv = OrderService()
    return await order_srv.order_create(_, order)


@router.get("/order", tags=["工单管理"])
@unified_resp
async def order_list(_: Request, q: CustomerModel.OrderQuery = Depends()):
    order_srv = OrderService()
    return await order_srv.order_list(_, q)


@router.delete("/order/{order_id}", tags=["工单管理"])
@unified_resp
async def order_delete(_: Request, order_id: str):
    order_srv = OrderService()
    return await order_srv.order_delete(order_id)


@router.put("/order/{order_id}", tags=["工单管理"])
@unified_resp
async def order_update(
    _: Request,
    order_id: str,
    order: CustomerModel.OrderUpdate,
):
    order_srv = OrderService()
    return await order_srv.order_update(order_id, order)


@router.get("/sync_data", tags=["工单管理"])
@unified_resp
async def order_list(_: Request):
    return await AptLarkTable().get_records()