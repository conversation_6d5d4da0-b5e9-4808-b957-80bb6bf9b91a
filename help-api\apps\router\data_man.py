"""
    数据管理接口，包括视频、音频、信令等数据的查询
"""

from typing import Annotated

from fastapi import APIRouter, Request, Query, Header
from fastapi.responses import StreamingResponse, FileResponse

from apps.common import unified_resp, HttpResp, AppException
from apps.models.common import ObjectIdStr
import apps.models.data_man as DModel
import apps.services.data_man as DMService
from apps.services.permissions import gen_dp, FuncId


router = APIRouter(prefix="/data_manage", tags=["数据管理"])


@router.get("/vehicles", dependencies=gen_dp(FuncId.DataManage))
@unified_resp
async def vehicle_list(_: Request):
    return await DMService.vehicle_list()


@router.get("/media/video", dependencies=gen_dp(FuncId.DataManage))
@unified_resp
async def video_query(_: Request, q: Annotated[DModel.VideoQuery, Query()]):
    return await DMService.query_video_data(q)


@router.get("/media/video/play/{video_id}.mp4")
async def video_segment_play(req: Request, video_id: ObjectIdStr, range: str = Header(None)) -> StreamingResponse:
    return await DMService.get_video_segment_data(video_id, range)


@router.get("/media/video/download/{video_id}.mp4")
async def video_segment_download(_: Request, video_id: ObjectIdStr) -> FileResponse:
    video = await DMService.get_video_data_by_id(video_id)
    if not video.exists:
        raise AppException(HttpResp.VIDEO_NOT_FOUND, "video segment file not found.")
    return FileResponse(video.path, media_type="video/mp4")


@router.get("/media/video/cover/{video_id}.jpg")
async def video_segment_cover_get(_: Request, video_id: ObjectIdStr) -> FileResponse:
    video = await DMService.get_video_data_by_id(video_id)
    if not video.cover_exists:
        raise AppException(HttpResp.VIDEO_NOT_FOUND, "video cover data not found.")
    return FileResponse(video.cover_path, media_type="image/jpeg")
