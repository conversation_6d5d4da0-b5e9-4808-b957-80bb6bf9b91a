"""
    内部接口，模块直接调用，不需要鉴权
"""

from fastapi import APIRouter, Request
import apps.services.internal as InternalService

router = APIRouter(tags=["内部接口"])


@router.post("/init_data")
async def init_db_data(req: Request):
    """初始化业务运行必要数据"""
    srv = InternalService.InitDataBase()
    if await srv.init_data():
        return {"msg": "Initialization successful"}
    return {"msg": "It's already initialized"}


# @router.websocket("/data_manager/ws")
# async def vehicle_device_endpoint(wsock: WebSocket):
#     """处理数据收集模块的 WebScoket 连接"""
#     return await WService.process_data_manage(wsock)
