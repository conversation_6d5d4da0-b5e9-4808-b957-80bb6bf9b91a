from typing import Annotated
from fastapi import APIRouter, Form, Query, Request, UploadFile
from fastapi.responses import StreamingResponse, FileResponse

import apps.models.material as MaterialModel
import apps.services.material as MaterialService
from apps.models.common import ObjectIdStr
from apps.common import unified_resp, HttpResp, AppException

router = APIRouter(prefix="/material", tags=["素材管理"])


@router.get("/cate_list", tags=["素材管理"])
@unified_resp
async def get_cate_list(_: Request, type: int = 1):
    return await MaterialService.get_cate_list(type)


@router.post("/cate_add", tags=["素材管理"])
@unified_resp
async def add_cate(_: Request, data: MaterialModel.MaterialCate):
    return await MaterialService.add_cate(data)


@router.delete("/cate_delete/{cate_id}", tags=["素材管理"])
@unified_resp
async def delete_cate(_: Request, cate_id: int):
    return await MaterialService.delete_cate(cate_id)


@router.put("/cate_update/{cate_id}", tags=["素材管理"])
@unified_resp
async def update_cate(_: Request, cate_id: int, data: MaterialModel.MaterialCateEdit):
    return await MaterialService.update_cate(cate_id, data)


@router.post("/upload", tags=["素材管理"])
@unified_resp
async def upload_material(file: UploadFile, cid: int = Form(...), type: int = Form(...)):
    return await MaterialService.upload_material(file, type, cid)


@router.get("/media_list", tags=["素材管理"])
@unified_resp
async def media_list(_: Request, q: Annotated[MaterialModel.MaterialQuery, Query()]):
    return await MaterialService.get_material_list(q)


@router.get("/content/{material_type}/{material_id}", tags=["素材管理"])
async def material_cover_get(_: Request, material_id: ObjectIdStr, material_type: str):
    material = await MaterialService.get_material_data_by_id(material_id, material_type)
    if material_type == "image" or material_type == "cover":
        return FileResponse(material, media_type="image/jpeg")
    if material_type == "video":
        return FileResponse(material, media_type="video/mp4")


@router.delete("/delete", tags=["素材管理"])
@unified_resp
async def material_delete(_: Request, material_ids: list[ObjectIdStr]):
    return await MaterialService.delete_material(material_ids)
