from fastapi import APIRouter, Request

from apps.common import unified_resp
import apps.models.metadata as MetaModel
import apps.services.metadata as MetaService
from apps.services.permissions import gen_dp, FuncId


router = APIRouter(prefix="/metadata", tags=["元数据管理"])


@router.get("/", dependencies=gen_dp(FuncId.MetadataManage))
@unified_resp
async def list_all(_: Request):
    """查询所有元数据"""
    return await MetaService.list_all()


@router.post("/", dependencies=gen_dp(FuncId.MetadataManage))
@unified_resp
async def create_data(_: Request, data: MetaModel.BaseMeta):
    """新增元数据"""
    return await MetaService.add_metadata(data)


@router.get("/{key_name}")
@unified_resp
async def get_data(_: Request, key_name: str):
    """查询指定 key 的元数据"""
    return await MetaService.get_metadata(key_name)


@router.put("/{key_name}", dependencies=gen_dp(FuncId.MetadataManage))
@unified_resp
async def update_data(_: Request, key_name: str, data: MetaModel.BaseMeta):
    """更新元数据"""
    return await MetaService.update_metadata(key_name, data)


@router.delete("/{key_name}", dependencies=gen_dp(FuncId.MetadataManage))
@unified_resp
async def delete_data(_: Request, key_name: str):
    """删除元数据"""
    return await MetaService.delete_metadata(key_name)
