from typing import Annotated

from fastapi import APIRouter, Request, Query

from apps.common import unified_resp
from apps.models.common import ObjectIdStr
import apps.models.user as UserModel
import apps.models.opconsole as OPCModel
import apps.services.opconsole as OPCService
from apps.services.permissions import FuncId, RoleResourceFlag, gen_dp


router = APIRouter(prefix="/op_consoles", tags=["操作台管理"])


@router.post("/", dependencies=gen_dp(FuncId.VehicleManage))
@unified_resp
async def add(_: Request, data: OPCModel.Add):
    """新增操作台"""
    return await OPCService.add(data=data)


@router.get("/", dependencies=gen_dp(FuncId.VehicleView))
@unified_resp
async def list_opc(req: Request, q: Annotated[OPCModel.Query, Query()]):
    """操作台列表"""
    user: UserModel.CacheInfo = req.state.user
    return await OPCService.query(q, user)


@router.put("/{opc_id}", dependencies=gen_dp(FuncId.VehicleManage))
@unified_resp
async def opc_update(_: Request, opc_id: ObjectIdStr, data: OPCModel.Update):
    """更新操作台"""
    return await OPCService.update(opc_id, data=data)


@router.delete("/{opc_id}", dependencies=gen_dp(FuncId.VehicleManage))
@unified_resp
async def delete_opc(_: Request, opc_id: ObjectIdStr):
    """删除操作台"""
    return await OPCService.delete(opc_id)


@router.put("/{opc_id}/device_bind", dependencies=gen_dp(FuncId.VehicleManage))
@unified_resp
async def opc_bind_device(_: Request, opc_id: ObjectIdStr, d: OPCModel.Device4OPCModel):
    """操作台绑定的操作台设备"""
    return await OPCService.bind_device(opc_id, d.device_id)


@router.get(
    "/{opc_id}/available_vehicles",
    dependencies=gen_dp(FuncId.VehicleView, RoleResourceFlag.OPERATOR),
)
@unified_resp
async def opc_available_vehicles_list(req: Request, opc_id: ObjectIdStr):
    """获取操作台绑定的车辆列表"""
    user: UserModel.CacheInfo = req.state.user
    vehicle_android_list = await OPCService.find_available_vehicles(opc_id, user)
    params_list = await OPCService.get_vehicles_android_params(vehicle_android_list, opc_id)
    return params_list


@router.get(
    "/{opc_id}/locked_vehicles",
    dependencies=gen_dp(FuncId.VehicleView, RoleResourceFlag.OPERATOR),
)
@unified_resp
async def get_locked_vehicles(_: Request, opc_id: ObjectIdStr):
    """获取操作台锁定的车辆列表"""
    return await OPCService.get_locked_vehicles(opc_id=opc_id)


@router.post(
    "/{opc_id}/lock_vehicle/{vehicle_id}",
    dependencies=gen_dp(FuncId.VehicleView, RoleResourceFlag.OPERATOR),
)
@unified_resp
async def op_console_lock_vehicle(req: Request, opc_id: ObjectIdStr, vehicle_id: ObjectIdStr):
    """锁定操作台与车"""
    user = req.state.user
    return await OPCService.lock_vehicle(opc_id, vehicle_id, user)


@router.post(
    "/{opc_id}/unlock_vehicle/{vehicle_id}",
    dependencies=gen_dp(FuncId.VehicleView, RoleResourceFlag.OPERATOR),
)
@unified_resp
async def op_console_unlock_vehicle(_: Request, opc_id: ObjectIdStr, vehicle_id: ObjectIdStr):
    """解锁操作台与车"""
    return await OPCService.unlock_vehicle(opc_id=opc_id, vehicle_id=vehicle_id)
