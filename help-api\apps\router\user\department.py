from typing import Optional

from fastapi import APIRouter, Request, Depends
from bson import ObjectId

from apps.common import unified_resp
from apps.models.common import ObjectIdStr
from apps.services.permissions import gen_dp, FuncId

from apps.services.department import DepartmentService
import apps.models.department as DepartmentModel


router = APIRouter(prefix="/department", tags=["部门管理"])


@router.post("", dependencies=gen_dp(FuncId.RoleManage))
@unified_resp
async def create_department(_: Request, data: DepartmentModel.DepartmentCreate):
    """创建部门"""
    return await DepartmentService().department_create(data)


@router.get("", dependencies=gen_dp(FuncId.RoleManage))
@unified_resp
async def list_department(
    _: Request,
    page_no: int = 1,
    page_size: int = 10,
    name: Optional[str] = None,
    is_enable: Optional[int] = None,
):
    """获取部门列表"""
    page = DepartmentModel.PagingModel(page_no=page_no, page_size=page_size)
    query = DepartmentModel.DepartmentQuery(name=name, is_enable=is_enable)
    return await DepartmentService().department_list(page, query)


@router.get("/tree")
@unified_resp
async def tree_department(_: Request):
    """获取部门树形结构"""
    return await DepartmentService().department_tree()


@router.get("/{dept_id}", dependencies=gen_dp(FuncId.RoleManage))
@unified_resp
async def detail_department(_: Request, dept_id: ObjectIdStr):
    """获取部门详情"""
    return await DepartmentService().department_detail(dept_id)


@router.put("/{dept_id}", dependencies=gen_dp(FuncId.RoleManage))
@unified_resp
async def update_department(_: Request, dept_id: ObjectIdStr, data: DepartmentModel.DepartmentUpdate):
    """更新部门信息"""
    return await DepartmentService().department_update(dept_id, data)


@router.delete("/{dept_id}", dependencies=gen_dp(FuncId.RoleManage))
@unified_resp
async def delete_department(_: Request, dept_id: ObjectIdStr):
    """删除部门"""
    return await DepartmentService().department_delete(dept_id)