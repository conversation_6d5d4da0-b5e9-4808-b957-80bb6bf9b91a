from fastapi import APIRouter, Request, Depends

from apps.common import unified_resp
from apps.models.common import PagingModel, ObjectIdStr
import apps.models.user as UserModel
import apps.services.role as RoleService
from apps.services.permissions import gen_dp, FuncId, RoleResourceFlag


router = APIRouter(prefix="/user/role", tags=["角色管理"], dependencies=gen_dp(FuncId.RoleManage))


@router.post("/", dependencies=gen_dp(FuncId.RoleManage))
@unified_resp
async def create(_: Request, role: UserModel.RoleCreate):
    """创建一个角色"""
    return await RoleService.role_create(role)


@router.get("/", dependencies=gen_dp(FuncId.RoleManage))
@unified_resp
async def list_query(_: Request, q: PagingModel = Depends()):
    """获取角色列表"""
    return await RoleService.role_list(q)


@router.get("/all", dependencies=gen_dp(FuncId.RoleManage))
@unified_resp
async def list_all(_: Request):
    """功能和role_list一样, 只是返回所有角色, 没有分页"""
    return await RoleService.role_list()


@router.get("/permissions", dependencies=gen_dp(FuncId.RoleManage))
@unified_resp
async def list_permission(_: Request):
    """获取所有权限ID"""
    return {
        "apis": [{"name": f.name, "id": f.value} for f in FuncId],
        "resources_flag": [{"name": f.name, "id": f.value} for f in RoleResourceFlag],
    }


@router.delete("/{role_id}", dependencies=gen_dp(FuncId.RoleManage))
@unified_resp
async def delete(_: Request, role_id: ObjectIdStr):
    """删除一个角色"""
    return await RoleService.role_delete(role_id)


@router.get("/{role_id}", dependencies=gen_dp(FuncId.RoleManage))
@unified_resp
async def detail(_: Request, role_id: ObjectIdStr):
    """获取角色详情"""
    return await RoleService.role_detail(role_id)


@router.put("/{role_id}", dependencies=gen_dp(FuncId.RoleManage))
@unified_resp
async def update(_: Request, role_id: ObjectIdStr, data: UserModel.RoleUpdate):
    """更新角色基本信息"""
    return await RoleService.role_update(role_id, data)


@router.put("/{role_id}/menu", dependencies=gen_dp(FuncId.RoleManage))
@unified_resp
async def update_menu(_: Request, role_id: ObjectIdStr, data: UserModel.RoleUpdateMenus):
    """更新角色菜单"""
    return await RoleService.role_update(role_id, data)


@router.put("/{role_id}/permissions", dependencies=gen_dp(FuncId.RoleManage))
@unified_resp
async def update_api(_: Request, role_id: ObjectIdStr, data: UserModel.RoleUpdatePermission):
    """更新角色API权限"""
    return await RoleService.role_update(role_id, data)


@router.put("/{role_id}/department", dependencies=gen_dp(FuncId.RoleManage))
@unified_resp
async def update_role_department(_: Request, role_id: ObjectIdStr, data: dict):
    """更新角色所属部门"""
    return await RoleService.role_update(role_id, {"department_id": data.get("department_id")})


@router.put("/{role_id}/departments", dependencies=gen_dp(FuncId.RoleManage))
@unified_resp
async def update_role_departments(_: Request, role_id: ObjectIdStr, data: dict):
    """更新角色可见/管理的部门列表"""
    return await RoleService.role_update(role_id, {"departments": data.get("departments", [])})


@router.put("/{role_id}/resource", dependencies=gen_dp(FuncId.RoleManage))
@unified_resp
async def update_resource(_: Request, role_id: ObjectIdStr, data: UserModel.RoleUpdateResource):
    """更新角色资源，车辆或者操作台"""
    return await RoleService.role_update(role_id, data)
