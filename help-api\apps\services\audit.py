import time
from typing import Dict

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import Message

from apps.db.mongo_db import MongoDB
from apps.models.user import CacheInfo
import apps.models.audit as AModels


COLL = MongoDB.get_collection("audit")
RECORD_METHOD = ("POST", "PUT", "DELETE")


class AuditMiddleware(BaseHTTPMiddleware):

    def __init__(self, app):
        super().__init__(app)

    async def set_body(self, request: Request):
        receive_ = await request._receive()

        async def receive() -> Message:
            return receive_

        request._receive = receive

    async def dispatch(self, request, call_next):
        start_time = time.perf_counter()
        # body_str = await self.get_body(request)
        response = await call_next(request)
        process_time = time.perf_counter() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        # await self.record_action(request, body_str)
        return response

    async def get_body(self, request: Request) -> str:
        """获取请求体"""
        if request.method not in RECORD_METHOD:
            # 不记录日志
            return ""
        await self.set_body(request)
        body_data = await request.body()
        # 过滤敏感信息
        if request.url.path.startswith("/api/v1/user") and "password" in body_data.decode("utf-8"):
            return '{"msg": "sensitive msg"}'
        return body_data.decode("utf-8")

    async def record_action(self, request: Request, body_str: str):
        if request.method not in RECORD_METHOD:
            return
        try:
            user: CacheInfo = request.state.user
            track_info: Dict = request.state.track_info
        except AttributeError:
            return
        record = AModels.Record(
            path=request.url.path,
            method=request.method,
            user_id=user.id,
            code=track_info["code"],
            msg=track_info["msg"],
            body=body_str,
            params=f"{request.query_params}",
        )
        await COLL.insert_one(record.model_dump())


async def query(q: AModels.Query):
    """查询操作记录"""
    limit = q.page_size
    skip_no = (q.page_no - 1) * q.page_size
    filter_d = {}
    if q.user_id:
        filter_d["user_id"] = q.user_id
    if q.path:
        filter_d["path"] = q.path
    if q.method:
        filter_d["method"] = q.method
    if q.start_time and q.end_time:
        filter_d["created_time"] = {"$gte": q.start_time, "$lte": q.end_time}
    count = await COLL.count_documents(filter_d)
    if count == 0:
        return {"count": 0, "lists": []}

    docs = COLL.aggregate(
        [
            {
                "$lookup": {
                    "from": "users",
                    "localField": "user_id",
                    "foreignField": "_id",
                    "as": "U",
                },
            },
            {"$unwind": "$U"},
            {"$addFields": {"username": "$U.username", "nickname": "$U.nickname"}},
            {"$match": filter_d},
            {"$skip": skip_no},
            {"$limit": limit},
            {"$project": {"U": 0}},
        ]
    )
    data = []
    async for doc in docs:
        data.append(doc)
    return {"count": count, "lists": data}
