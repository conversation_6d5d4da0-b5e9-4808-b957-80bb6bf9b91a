import base64
import json
import time

from bson import ObjectId
from fastapi import Request
import httpx
from motor.core import AgnosticCollection
from pymongo.errors import DuplicateKeyError

from apps.db import MongoDB
from apps.common import HttpResp, AppException
from apps.utils import singleton
from apps.utils.tools import Tools
from apps.utils.email_utils import EmailService
import apps.models.customer_jp as CustomerModel

import hashlib
import hmac
import arrow


@singleton
class OperationService:
    """操作管理服务"""

    def __init__(self, _=None):
        self.collection: AgnosticCollection = MongoDB["operation_jp"]

    async def get_next_id(self):
        """获取下一个现象ID"""
        cursor = self.collection.find({}, {"_id": 1})
        data = await cursor.to_list(None)
        max_id = max([int(item["_id"][1:]) for item in data]) if data else 2000
        max_id = max(max_id, 2000)
        return "S" + str(max_id + 1)

    async def operation_create(self, operation: CustomerModel.OperationCreate) -> dict:
        operation_json = operation.model_dump()
        next_id = await self.get_next_id()
        operation_json.update(
            {
                "_id": next_id,
                "create_time": arrow.utcnow().datetime,
                "update_time": arrow.utcnow().datetime,
            }
        )
        try:
            res = await self.collection.insert_one(operation_json)
        except DuplicateKeyError as exc:
            raise AppException(HttpResp.ROLE_NAME_REPEAT) from exc
        assert res.inserted_id, "Operation info insert failed."
        operation = CustomerModel.OperationOut.model_validate(operation_json)
        res_data = operation.model_dump()
        return res_data

    async def operation_list(self, query: CustomerModel.OperationQuery = None):
        if query is None:
            cursor = self.collection.find().sort("sort", -1)
        else:
            skip = (query.page_no - 1) * query.page_size
            cursor = self.collection.find().sort("sort", -1).skip(skip).limit(query.page_size)
        total_count = await self.collection.count_documents({})
        res_data = []
        async for operation in cursor:
            operation_obj = CustomerModel.OperationOut(**operation)
            res_data.append(operation_obj.model_dump())
        return {"count": total_count, "lists": res_data}

    async def operation_delete(self, rid):
        res = await self.collection.delete_one({"_id": rid})
        if res.deleted_count == 0:
            raise AppException(404, "Operation not exist.")
        return {"msg": "删除成功"}

    async def operation_update(self, rid, operation: CustomerModel.OperationCreate):
        data = operation.model_dump()
        data.update({"update_time": arrow.utcnow().datetime})
        res = await self.collection.update_one({"_id": rid}, {"$set": data})
        if res.modified_count == 0:
            raise AppException(404, "Operation not exist.")
        return {"name": operation.content, "msg": "修改成功"}


@singleton
class PhenomenonService:
    """现象管理服务"""

    def __init__(self, _=None):
        self.collection: AgnosticCollection = MongoDB["phenomenon_jp"]

    async def get_next_id(self):
        """获取下一个现象ID"""
        cursor = self.collection.find({}, {"_id": 1})
        data = await cursor.to_list(None)
        max_id = max([int(item["_id"][1:]) for item in data]) if data else 2000
        max_id = max(max_id, 2000)
        return "P" + str(max_id + 1)

    async def phenomenon_create(self, phenomenon: CustomerModel.PhenomenonCreate) -> dict:
        phenomenon_json = phenomenon.model_dump()
        next_id = await self.get_next_id()
        phenomenon_json.update(
            {
                "_id": next_id,
                "create_time": arrow.utcnow().datetime,
                "update_time": arrow.utcnow().datetime,
            }
        )
        try:
            res = await self.collection.insert_one(phenomenon_json)
        except DuplicateKeyError as exc:
            raise AppException(412, "phenomenon name repeat") from exc
        assert res.inserted_id, "phenomenon info insert failed."
        return {"name": phenomenon.content}

    async def phenomenon_list(self):
        cursor = self.collection.find({}).sort("sort", -1)
        phenomenon_list = await cursor.to_list(None)
        res_data = [CustomerModel.PhenomenonOut(**item).model_dump() for item in phenomenon_list]
        return Tools.list_to_tree(res_data, "id", "pid", "children")

    async def phenomenon_delete(self, id_):
        res = await self.collection.delete_one({"_id": id_})
        if res.deleted_count == 0:
            raise AppException(404, "phenomenon not exist.")
        return {"msg": "删除成功"}

    async def phenomenon_update(self, phenomenon_id, phenomenon: CustomerModel.PhenomenonUpdate):
        data = json.loads(phenomenon.model_dump_json())
        data["update_time"] = arrow.utcnow().datetime
        del data["id"]
        await self.collection.update_one({"_id": phenomenon_id}, {"$set": data})
        return {"name": phenomenon.content, "msg": "修改成功"}


@singleton
class ConsumerService:
    """用户管理服务"""

    def __init__(self, _=None):
        self.collection: AgnosticCollection = MongoDB["consumer_jp"]

    async def get_next_id(self):
        """获取下一个用户ID"""
        cursor = self.collection.find({}, {"_id": 1})
        data = await cursor.to_list(None)
        max_id = max([int(item["_id"][1:]) for item in data]) if data else 10000
        max_id = max(max_id, 10000)
        return "U" + str(max_id + 1)

    async def consumer_create(self, req: Request, consumer: CustomerModel.ConsumerCreate) -> dict:
        user_info = req.state._state
        consumer_json = consumer.model_dump()
        next_id = await self.get_next_id()
        consumer_json.update(
            {
                "_id": next_id,
                "create_username": user_info['user'].username,
                "create_role_id": user_info['user'].role_ids,
                "create_time": arrow.utcnow().datetime,
                "update_time": arrow.utcnow().datetime,
            }
        )
        try:
            res = await self.collection.insert_one(consumer_json)
        except DuplicateKeyError as exc:
            raise AppException(HttpResp.ROLE_NAME_REPEAT) from exc
        assert res.inserted_id, "consumer info insert failed."
        consumer = CustomerModel.ConsumerOut.model_validate(consumer_json)
        res_data = consumer.model_dump()
        return res_data

    async def consumer_list(self, req: Request, query: CustomerModel.ConsumerQuery = None):
        # 初始化过滤条件
        filter_d = {}
        
        # 应用数据权限过滤
        user_info = req.state._state
        from apps.services.department import DepartmentService
        permission_filter = await DepartmentService().get_data_permission_filter(user_info)
        filter_d.update(permission_filter)

        if query:
            if query.keyword:
                filter_d["$or"] = [{"name": {"$regex": query.keyword}}, {"phone": {"$regex": query.keyword}}]

            skip = (query.page_no - 1) * query.page_size
            cursor = self.collection.find(filter_d).skip(skip).limit(query.page_size)

        else:
            cursor = self.collection.find(filter_d)

        total_count = await self.collection.count_documents({})
        res_data = []
        async for consumer in cursor:
            print(consumer)
            consumer_obj = CustomerModel.ConsumerOut(**consumer)
            res_data.append(consumer_obj.model_dump())
        return {"count": total_count, "lists": res_data}

    async def consumer_delete(self, rid):
        res = await self.collection.delete_one({"_id": rid})
        if res.deleted_count == 0:
            raise AppException(404, "consumer not exist.")
        return {"msg": "删除成功"}

    async def consumer_update(self, cid, consumer: CustomerModel.ConsumerCreate):
        data = consumer.model_dump()
        data.update({"update_time": arrow.utcnow().datetime})
        res = await self.collection.update_one({"_id": cid}, {"$set": data})
        if res.modified_count == 0:
            raise AppException(404, "consumer not exist.")
        return {"name": consumer.name, "msg": "修改成功"}


@singleton
class OrderService:
    """工单管理服务"""

    def __init__(self, _=None):
        self.collection: AgnosticCollection = MongoDB["work_order_jp"]
        self.WEBHOOK_URL = "https://open.feishu.cn/open-apis/bot/v2/hook/d2736e7a-de1a-498a-a9b2-1ca3c3bc753c"
        self.WEBHOOK_SECRET = "kwNznJAq9TqE6znNoX1iL"

    async def get_next_id(self):
        """获取下一个工单ID"""
        cursor = self.collection.find({}, {"_id": 1})
        data = await cursor.to_list(None)
        max_id = max([int(item["_id"][1:]) for item in data]) if data else 10000
        max_id = max(max_id, 10000)
        return "O" + str(max_id + 1)

    async def order_create(self, req: Request, order: CustomerModel.OrderCreate) -> dict:
        user_info = req.state._state
        order_json = order.model_dump()
        now = arrow.utcnow().datetime
        next_id = await self.get_next_id()
        order_json.update(
            {
                "_id": next_id,
                "create_username": user_info['user'].username,
                "create_role_id": user_info['user'].role_ids,
                "create_time": now,
                "update_time": now,
                "completion_time": None,
                "timeline": [
                    {"timestamp": now, "content": "新規作成"},
                ],
            }
        )
        try:
            res = await self.collection.insert_one(order_json)
        except DuplicateKeyError as exc:
            raise AppException(404, "Order name repeat") from exc
        assert res.inserted_id, "Order info insert failed."
        order = CustomerModel.OrderOut.model_validate(order_json)
        res_data = order.model_dump()
        return res_data

    async def order_list(self, req: Request, query: CustomerModel.OrderQuery = None):
        # 使用DepartmentService的get_data_permission_filter方法获取数据权限过滤条件
        from apps.services.department import DepartmentService
        filter_d = await DepartmentService().get_data_permission_filter(req.state._state)

        if query:
            if query.status is not None:
                if query.status == 5:
                    filter_d["status"] = {"$in": [1, 2, 4]}
                elif query.status in [1, 2, 3, 4]:
                    filter_d["status"] = query.status
            else:
                filter_d["status"] = {"$in": [1, 2, 3, 4]}

            if query.keyword:
                filter_d["$or"] = [{"customer_name": {"$regex": query.keyword}}, {"contact_info": {"$regex": query.keyword}}]

            if query.customer_id:
                filter_d["customer_id"] = query.customer_id

            if query.phenomenon:
                phenomenon_collection = MongoDB["phenomenon_jp"]
                phenomenon_cursor = phenomenon_collection.find({"content": {"$regex": query.phenomenon}})
                phenomenon_ids = []
                async for phenomenon in phenomenon_cursor:
                    phenomenon_ids.append(phenomenon["_id"])
                if phenomenon_ids:
                    filter_d["phenomenon_id"] = {"$in": phenomenon_ids}
                else:
                    filter_d["phenomenon_id"] = {"$in": [None]}

            skip = (query.page_no - 1) * query.page_size
            cursor = self.collection.find(filter_d).sort("create_time", -1).skip(skip).limit(query.page_size)

        else:
            cursor = self.collection.find(filter_d).sort("create_time", -1)
        total_count = await self.collection.count_documents(filter_d)
        res_data = []
        async for order in cursor:
            order_obj = CustomerModel.OrderOut(**order)
            res_data.append(order_obj.model_dump())
        return {"count": total_count, "lists": res_data}

    async def order_delete(self, rid):
        res = await self.collection.delete_one({"_id": rid})
        if res.deleted_count == 0:
            raise AppException(404, "Order not exist.")
        return {"msg": "削除に成功しました"}

    async def order_update(self, rid, order: CustomerModel.OrderUpdate):
        data = order.model_dump()


        if data.get("status") == 2:
            if data.get("timeline_content"):
                data["timeline"].append({"timestamp": arrow.utcnow().datetime, "content": data.get("timeline_content")})
                data.pop("timeline_content")

        # 解决完了
        if data.get("status") == 3:
            data["timeline"].append({"timestamp": arrow.utcnow().datetime, "content": "解決完了"})
            data.update({"completion_time": arrow.utcnow().datetime})
            
            # 获取部门用户邮箱列表
            email_list = await EmailService().get_department_user_emails(data.get("create_username"))
            
            if email_list:
                # 获取现象描述列表并合并为字符串
                phenomenon_id_list = data.get("phenomenon_id", [])
                phenomenon = ""
                if phenomenon_id_list:
                    try:
                        phenomenon_collection = MongoDB["phenomenon_jp"]
                        # 使用批量查询一次性获取所有现象
                        phenomenon_cursor = phenomenon_collection.find({"_id": {"$in": phenomenon_id_list}}, {"content": 1})
                        phenomenon_docs = await phenomenon_cursor.to_list(length=None)
                        # 提取内容并合并
                        phenomenon_list = [doc.get("content", "") for doc in phenomenon_docs if "content" in doc]
                        phenomenon = ",".join(phenomenon_list)
                    except Exception as e:
                        print(f"获取现象描述时出错: {str(e)}")

                resolveID = data.get("resolve")
                resolve_collection = MongoDB["operation_jp"]
                resolve_doc = await resolve_collection.find_one({"_id": resolveID})
                if not resolve_doc:
                    print(f"未找到解决方案: {resolveID}")
                    resolve = ""
                else:
                    resolve = resolve_doc["content"]

                # 使用邮件模板并传入客户信息
                email_service = EmailService()
                email_data = email_service.get_completion_email_template(
                    customer_name=data.get("customer_name", ""),
                    device_control_name=data.get("device_control_name", ""),
                    device_vehicle_name=data.get("device_vehicle_name", ""),
                    phenomenon=phenomenon,
                    resolve=resolve,
                )
                await email_service.send_email(email_list, email_data)


        # 拓疆者处理中
        if data.get("status") == 4:
            data["timeline"].append({"timestamp": arrow.utcnow().datetime, "content": "BuilderXが至急対応中です。"})

            # 获取部门用户邮箱列表
            email_list = await EmailService().get_department_user_emails(data.get("create_username"))
            
            print(email_list)
            if email_list:
                # 获取现象描述列表并合并为字符串
                phenomenon_id_list = data.get("phenomenon_id", [])
                phenomenon = ""
                if phenomenon_id_list:
                    try:
                        phenomenon_collection = MongoDB["phenomenon_jp"]
                        # 使用批量查询一次性获取所有现象
                        phenomenon_cursor = phenomenon_collection.find({"_id": {"$in": phenomenon_id_list}}, {"content": 1})
                        phenomenon_docs = await phenomenon_cursor.to_list(length=None)
                        # 提取内容并合并
                        phenomenon_list = [doc.get("content", "") for doc in phenomenon_docs if "content" in doc]
                        phenomenon = ",".join(phenomenon_list)
                    except Exception as e:
                        print(f"获取现象描述时出错: {str(e)}")


                # 使用邮件模板并传入客户信息
                email_service = EmailService()
                email_data = email_service.get_processing_email_template(
                    customer_name=data.get("customer_name", ""),
                    device_control_name=data.get("device_control_name", ""),
                    device_vehicle_name=data.get("device_vehicle_name", ""),
                    phenomenon=phenomenon,
                )
                await email_service.send_email(email_list, email_data)

            # 通过飞书发送消息
            sign = self.gen_sign(self.WEBHOOK_SECRET)
            params = {
                "timestamp": int(arrow.utcnow().datetime.timestamp()),
                "sign": sign,
                "msg_type": "interactive",
                "card": {
                    "config": {"update_multi": True},
                    "i18n_elements": {
                        "zh_cn": [
                            {
                                "tag": "column_set",
                                "flex_mode": "none",
                                "background_style": "default",
                                "horizontal_spacing": "8px",
                                "horizontal_align": "left",
                                "columns": [
                                    {
                                        "tag": "column",
                                        "width": "weighted",
                                        "vertical_align": "top",
                                        "vertical_spacing": "8px",
                                        "background_style": "default",
                                        "elements": [
                                            {
                                                "tag": "markdown",
                                                "content": f"**👨‍🔧客户名称：**\n{data.get('customer_name')}",
                                                "text_align": "left",
                                                "text_size": "normal",
                                            }
                                        ],
                                        "weight": 1,
                                    },
                                    {
                                        "tag": "column",
                                        "width": "weighted",
                                        "vertical_align": "top",
                                        "vertical_spacing": "8px",
                                        "background_style": "default",
                                        "elements": [
                                            {
                                                "tag": "markdown",
                                                "content": f"**📱联系方式：**\n{data.get('contact_info')}",
                                                "text_align": "left",
                                                "text_size": "normal",
                                            }
                                        ],
                                        "weight": 1,
                                    },
                                ],
                                "margin": "16px 0px 0px 0px",
                            },
                            {
                                "tag": "column_set",
                                "flex_mode": "stretch",
                                "background_style": "default",
                                "horizontal_spacing": "8px",
                                "horizontal_align": "left",
                                "columns": [
                                    {
                                        "tag": "column",
                                        "width": "weighted",
                                        "vertical_align": "top",
                                        "vertical_spacing": "8px",
                                        "background_style": "default",
                                        "elements": [
                                            {
                                                "tag": "markdown",
                                                "content": f"**🚀设备名称：**\n{data.get('device_control_name')}/{data.get('device_vehicle_name')}",
                                                "text_align": "left",
                                                "text_size": "normal",
                                            }
                                        ],
                                        "weight": 1,
                                    },
                                    {
                                        "tag": "column",
                                        "width": "weighted",
                                        "vertical_align": "top",
                                        "vertical_spacing": "8px",
                                        "background_style": "default",
                                        "elements": [
                                            {
                                                "tag": "markdown",
                                                "content": f"**📒现象描述：**\n{data.get('phenomenon_description')}",
                                                "text_align": "left",
                                                "text_size": "normal",
                                            }
                                        ],
                                        "weight": 1,
                                    },
                                ],
                                "margin": "16px 0px 0px 0px",
                            },
                            {
                                "tag": "action",
                                "actions": [
                                    {
                                        "tag": "button",
                                        "text": {"tag": "plain_text", "content": "点击查看"},
                                        "type": "danger",
                                        "complex_interaction": True,
                                        "width": "default",
                                        "size": "small",
                                        "icon": {"tag": "standard_icon", "token": "mosaic-smear_outlined"},
                                        "multi_url": {
                                            "url": "https://help.apps.jp.builderx.com",
                                            "pc_url": "",
                                            "ios_url": "",
                                            "android_url": "",
                                        },
                                    }
                                ],
                            },
                        ]
                    },
                    "i18n_header": {
                        "zh_cn": {
                            "title": {"tag": "plain_text", "content": "【工单反馈】有一条新的工单反馈"},
                            "subtitle": {"tag": "plain_text", "content": ""},
                            "template": "red",
                        }
                    },
                },
            }
            async with httpx.AsyncClient() as client:
                resp = await client.post(self.WEBHOOK_URL, json=params)
                resp.raise_for_status()
                result = resp.json()
                if result.get("code") and result.get("code") != 0:
                    print(f"发送失败：{result['msg']}")
                    return

        data.update({"update_time": arrow.utcnow().datetime})
        res = await self.collection.update_one({"_id": rid}, {"$set": data})
        if res.modified_count == 0:
            raise AppException(404, "Order not exist.")
        item = await self.collection.find_one({"_id": rid})
        return {"content": CustomerModel.OrderOut(**item).model_dump(), "msg": "修改成功"}




    def gen_sign(self, secret):
        string_to_sign = "{}\n{}".format(int(arrow.utcnow().datetime.timestamp()), secret)
        hmac_code = hmac.new(string_to_sign.encode("utf-8"), digestmod=hashlib.sha256).digest()
        sign = base64.b64encode(hmac_code).decode("utf-8")
        return sign


class AptLarkTable:
    tenant_token_time = 0
    tenant_access_token = None
    proxy = "http://*************:7890"

    def __init__(self):
        self.app_token = "DpygbQnPcasIpRsduZbcjHPznud"
        self.url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app_token}"
        self.c = httpx.AsyncClient(proxies=self.proxy)
        # self.c = httpx.AsyncClient()
        self.collection_phenomenon: AgnosticCollection = MongoDB["phenomenon_jp"]
        self.collection_operation: AgnosticCollection = MongoDB["operation_jp"]

    async def get_tenant_access_token(self) -> bool:
        if time.time() - self.tenant_token_time < 7000:
            return {
                "Authorization": f"Bearer {self.tenant_access_token}",
                "Content-Type": "application/json; charset=utf-8",
            }
        url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"

        data = {
            "app_id": "cli_a02e141ea9b9d013",
            "app_secret": "ylKboJnUR2JHyXrctyi4wezLcmaAQr1Q",
        }
        resp = await self.c.post(url, data=data)
        resp.raise_for_status()
        res_data = resp.json()
        self.tenant_access_token = res_data["tenant_access_token"]
        self.tenant_token_time = time.time()
        return {
            "Authorization": f"Bearer {self.tenant_access_token}",
            "Content-Type": "application/json; charset=utf-8",
        }

    async def get_records(self):
        await self.get_records_for_phenomenon()
        await self.get_records_for_operation()
        return {"msg": "获取成功"}

    async def get_records_for_phenomenon(self):
        header_token = await self.get_tenant_access_token()
        table_id = "tbl1WZNepZjRyDkZ"
        url_ = f"{self.url}/tables/{table_id}/records?view_id=vewx2P0Jor"
        resp = await self.c.get(url_, headers=header_token)
        resp.raise_for_status()
        res_data = resp.json()
        data = [item["fields"] for item in res_data.get("data", {}).get("items")]
        print(res_data)

        p_row_list = []

        for item in data:
            # 故障为None，跳过
            if item.get("故障") is None or item.get("故障类型") is None:
                continue
            fault_type = item.get("故障类型")
            if fault_type and fault_type not in p_row_list:
                p_row_list.append(fault_type)

        # 定义pid的行
        p_row_data = [
            {
                "_id": f"P100{index}",
                "pid": "0",
                "sort": 0,
                "operation_id": [],
                "content": msg,
                "description": "",
                "create_time": arrow.utcnow().datetime,
                "update_time": arrow.utcnow().datetime,
            }
            for index, msg in enumerate(p_row_list)
        ]

        c_row_data = []
        for item in data:
            # 故障为None，跳过
            if item.get("故障") is None or item.get("故障类型") is None:
                continue

            # 定义单条数据
            row = {
                "_id": item.get("故障_ID"),
                "pid": "",
                "sort": 0,
                "operation_id": [],
                "content": item.get("故障"),
                "description": item.get("故障现象/详情", "") if item.get("故障现象/详情") is not None else "",
                "create_time": arrow.utcnow().datetime,
                "update_time": arrow.utcnow().datetime,
            }

            # 更新pid
            for p_row in p_row_data:
                if item.get("故障类型") == p_row.get("content"):
                    row.update({"pid": p_row.get("_id")})

            for key, value in item.items():
                if "解决步骤ID" in key:
                    if value and value[0].get("text") is not None:
                        row["operation_id"].append(value[0].get("text"))

            c_row_data.append(row)

        # 合并两个数组
        row_data = p_row_data + c_row_data
        for row in row_data:
            if await self.collection_phenomenon.find_one({"_id": row["_id"]}):
                await self.collection_phenomenon.update_one({"_id": row["_id"]}, {"$set": row})
            else:
                await self.collection_phenomenon.insert_one(row)

    async def get_records_for_operation(self):
        header_token = await self.get_tenant_access_token()
        table_id = "tblnLF2ZRObgaKLx"
        url_ = f"{self.url}/tables/{table_id}/records?view_id=vew9qSgFLD"
        resp = await self.c.get(url_, headers=header_token)
        resp.raise_for_status()
        res_data = resp.json()
        data = [item["fields"] for item in res_data.get("data", {}).get("items")]

        for item in data:
            # 解决步骤为None，跳过
            if item.get("解决步骤-详情") is None:
                continue

            # 定义单条数据
            row = {
                "_id": item.get("解决步骤-ID"),
                "sort": 0,
                "content": item.get("解决步骤-详情"),
                "description": item.get("解决步骤-补充详情", "") if item.get("解决步骤-补充详情") is not None else "",
                "answer_type": 1,
                "device_type": 1,
                "is_need_tools": False,
                "is_need_detail": item.get("工具", "") if item.get("工具") is not None else "",
                "create_time": arrow.utcnow().datetime,
                "update_time": arrow.utcnow().datetime,
            }

            device_type_map = {
                "操作台": 1,
                "远端/车端": 2,
                "操作台+车端": 3,
                "服务器/机房": 4,
                None: 4,
            }
            row.update({"device_type": device_type_map.get(item.get("位置"))})

            answer_type_map = {
                "软件": 1,
                "硬件": 2,
                None: 3,
            }
            row.update({"answer_type": answer_type_map.get(item.get("软/硬件"))})

            is_need_tools_map = {
                "是": True,
                "否": False,
                None: False,
            }
            row.update({"is_need_tools": is_need_tools_map.get(item.get("是否工具"))})

            if await self.collection_operation.find_one({"_id": row["_id"]}):
                await self.collection_operation.update_one({"_id": row["_id"]}, {"$set": row})
            else:
                await self.collection_operation.insert_one(row)
