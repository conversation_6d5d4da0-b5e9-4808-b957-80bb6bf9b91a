import json
from typing import List, Optional, Dict, Any, Union

import arrow
from bson import ObjectId
from motor.core import AgnosticCollection
from pymongo.errors import DuplicateKeyError

from apps.db import MongoDB
from apps.common import HttpResp, AppException
from apps.utils import singleton
from apps.models.common import PagingModel
from apps.utils.tools import Tools
import apps.models.department as DepartmentModel


DEPT_COLL: AgnosticCollection = MongoDB.get_collection("departments")


@singleton
class DepartmentService:
    """部门管理服务"""

    def __init__(self, _=None):
        pass

    async def department_create(self, department: DepartmentModel.DepartmentCreate) -> dict:
        """创建部门"""
        dept_json = department.model_dump()
        
        # 检查部门名称是否重复
        if await DEPT_COLL.find_one({"name": department.name}):
            raise AppException(HttpResp.DEPT_NAME_REPEAT)
        
        # 设置部门层级
        level = 1
        if department.parent_id:
            # 检查父部门是否存在
            parent_dept = await DEPT_COLL.find_one({"_id": ObjectId(department.parent_id)})
            if not parent_dept:
                raise AppException(HttpResp.DEPT_PARENT_NOT_EXIST)
            
            # 检查部门层级是否超过最大限制(5级)
            level = parent_dept.get("level", 1) + 1
            if level > 5:
                raise AppException(HttpResp.DEPT_LEVEL_EXCEED)
        
        dept_json.update({
            "create_time": arrow.utcnow().datetime,
            "update_time": arrow.utcnow().datetime,
            "level": level
        })
        
        try:
            res = await DEPT_COLL.insert_one(dept_json)
        except DuplicateKeyError as exc:
            raise AppException(HttpResp.DEPT_NAME_REPEAT) from exc
        
        assert res.inserted_id, "Department info insert failed."
        return {"id": str(res.inserted_id), "name": department.name}

    async def department_list(self, page: Optional[PagingModel] = None, query: Optional[DepartmentModel.DepartmentQuery] = None):
        """获取部门列表"""
        filter_dict = {}
        if query:
            if query.name:
                filter_dict["name"] = {"$regex": query.name, "$options": "i"}
            if query.is_enable is not None:
                filter_dict["is_enable"] = query.is_enable
        
        if page:
            # 分页查询
            total = await DEPT_COLL.count_documents(filter_dict)
            cursor = DEPT_COLL.find(filter_dict).sort("sort", -1).skip((page.page_no - 1) * page.page_size).limit(page.page_size)
            dept_list = await cursor.to_list(None)
            return {
                "lists": [DepartmentModel.DepartmentOut(**item).model_dump() for item in dept_list],
                "page_no": page.page_no,
                "page_size": page.page_size,
                "total": total
            }
        else:
            # 查询所有
            cursor = DEPT_COLL.find(filter_dict).sort("sort", -1)
            dept_list = await cursor.to_list(None)
            return [DepartmentModel.DepartmentOut(**item).model_dump() for item in dept_list]

    async def department_tree(self):
        """获取部门树形结构"""
        cursor = DEPT_COLL.find().sort("sort", -1)
        dept_list = await cursor.to_list(None)
        res_data = [DepartmentModel.DepartmentOut(**item).model_dump() for item in dept_list]
        return Tools.list_to_tree(res_data, "id", "parent_id", "children")

    async def department_detail(self, dept_id: str) -> dict:
        """获取部门详情"""
        dept = await DEPT_COLL.find_one({"_id": ObjectId(dept_id)})
        if not dept:
            raise AppException(HttpResp.DEPT_NOT_EXIST)
        return DepartmentModel.DepartmentOut(**dept).model_dump()

    async def department_update(self, dept_id: str, department: DepartmentModel.DepartmentUpdate) -> dict:
        """更新部门信息"""
        # 检查部门是否存在
        dept = await DEPT_COLL.find_one({"_id": ObjectId(dept_id)})
        if not dept:
            raise AppException(HttpResp.DEPT_NOT_EXIST)
        
        # 检查部门名称是否重复
        if department.name and department.name != dept.get("name"):
            if await DEPT_COLL.find_one({"name": department.name, "_id": {"$ne": ObjectId(dept_id)}}):
                raise AppException(HttpResp.DEPT_NAME_REPEAT)
        
        # 检查父部门是否存在且不是自己
        if department.parent_id:
            if str(department.parent_id) == dept_id:
                raise AppException(HttpResp.DEPT_PARENT_SELF)
                
            parent_dept = await DEPT_COLL.find_one({"_id": ObjectId(department.parent_id)})
            if not parent_dept:
                raise AppException(HttpResp.DEPT_PARENT_NOT_EXIST)
            
            # 检查是否将部门设置为自己的子部门
            child_depts = await self._get_child_depts(dept_id)
            if str(department.parent_id) in [str(d["_id"]) for d in child_depts]:
                raise AppException(HttpResp.DEPT_PARENT_CHILD)
            
            # 检查部门层级是否超过最大限制(5级)
            level = parent_dept.get("level", 1) + 1
            if level > 5:
                raise AppException(HttpResp.DEPT_LEVEL_EXCEED)
            
            # 更新部门层级
            department.level = level
        
        data = department.model_dump(exclude_none=True)
        data["update_time"] = arrow.utcnow().datetime
        
        await DEPT_COLL.update_one({"_id": ObjectId(dept_id)}, {"$set": data})
        return {"id": dept_id, "msg": "修改成功"}

    async def department_delete(self, dept_id: str) -> dict:
        """删除部门"""
        # 检查部门是否存在
        dept = await DEPT_COLL.find_one({"_id": ObjectId(dept_id)})
        if not dept:
            raise AppException(HttpResp.DEPT_NOT_EXIST)
        
        # 检查是否有子部门
        child_count = await DEPT_COLL.count_documents({"parent_id": ObjectId(dept_id)})
        if child_count > 0:
            raise AppException(HttpResp.DEPT_HAS_CHILDREN)
        
        # 检查是否有关联的角色
        role_coll = MongoDB.get_collection("roles")
        role_count = await role_coll.count_documents({"department_id": ObjectId(dept_id)})
        if role_count > 0:
            raise AppException(HttpResp.DEPT_HAS_ROLES)
        
        # 检查是否有关联的角色部门权限
        role_dept_count = await role_coll.count_documents({"departments": ObjectId(dept_id)})
        if role_dept_count > 0:
            raise AppException(HttpResp.DEPT_HAS_ROLE_DEPTS)
        
        # 删除部门
        res = await DEPT_COLL.delete_one({"_id": ObjectId(dept_id)})
        if res.deleted_count == 0:
            raise AppException(HttpResp.DEPT_NOT_EXIST)
        
        return {"msg": "删除成功"}

    async def _get_child_depts(self, dept_id: str) -> List[Dict]:
        """获取所有子部门"""
        child_depts = await DEPT_COLL.find({"parent_id": ObjectId(dept_id)}).to_list(None)
        result = child_depts.copy()
        
        for child in child_depts:
            sub_children = await self._get_child_depts(str(child["_id"]))
            result.extend(sub_children)
        
        return result

    async def get_user_depts(self, user_id: str, include_child: bool = True) -> List[str]:
        """获取用户可访问的部门ID列表"""
        user_coll = MongoDB.get_collection("users")
        user = await user_coll.find_one({"_id": ObjectId(user_id)})
        if not user:
            return []
        
        # 超级管理员可以访问所有部门
        if str(user.get("_id")) == "aaaaaaaaaaaaaaaaaaaaaaaa":
            depts = await DEPT_COLL.find().to_list(None)
            return [str(dept["_id"]) for dept in depts]
        
        # 获取用户角色
        role_ids = user.get("role_ids", [])
        if not role_ids:
            return []
        
        # 获取角色关联的部门
        role_coll = MongoDB.get_collection("roles")
        roles = await role_coll.find({"_id": {"$in": role_ids}}).to_list(None)
        
        dept_ids = set()
        for role in roles:
            # 添加角色所属部门
            if role.get("department_id"):
                dept_ids.add(str(role["department_id"]))
            
            # 添加角色可访问的部门
            if role.get("departments"):
                dept_ids.update([str(dept_id) for dept_id in role["departments"]])
        
        # 如果需要包含子部门
        if include_child and dept_ids:
            for dept_id in list(dept_ids):
                child_depts = await self._get_child_depts(dept_id)
                dept_ids.update([str(dept["_id"]) for dept in child_depts])
        
        return list(dept_ids)
        
    async def get_data_permission_filter(self, user_info: Dict[str, Any]) -> Dict[str, Any]:
        """获取数据权限过滤条件
        
        根据用户信息获取数据权限过滤条件，用于在查询数据时进行权限过滤
        
        Args:
            user_info: 用户信息，包含用户对象
            
        Returns:
            过滤条件字典，可直接用于MongoDB查询
        """
        filter_dict = {}
        user_id = str(user_info['user'].id)
        
        # 超级管理员可以查看所有数据
        if user_id == "aaaaaaaaaaaaaaaaaaaaaaaa":
            return filter_dict  # 不添加过滤条件，查看所有数据
        
        # 获取用户可访问的部门ID列表
        dept_ids = await self.get_user_depts(user_id, include_child=True)
        
        # 如果用户没有部门权限，则只能查看自己创建的数据
        if not dept_ids:
            filter_dict["create_username"] = user_info['user'].username
        else:
            # 查询这些部门下的角色
            role_coll = MongoDB.get_collection("roles")
            dept_roles = await role_coll.find({"department_id": {"$in": [ObjectId(dept_id) for dept_id in dept_ids if dept_id]}}).to_list(None)
            role_ids = [role["_id"] for role in dept_roles]
            
            # 查询拥有这些角色的用户
            user_coll = MongoDB.get_collection("users")
            role_users = await user_coll.find({"role_ids": {"$in": role_ids}}).to_list(None)
            role_user_names = [user["username"] for user in role_users]
            
            # 如果当前用户不在角色用户列表中，添加当前用户
            if user_info['user'].username not in role_user_names:
                role_user_names.append(user_info['user'].username)
            
            # 查询这些用户创建的数据
            filter_dict["create_username"] = {"$in": role_user_names}
        
        return filter_dict