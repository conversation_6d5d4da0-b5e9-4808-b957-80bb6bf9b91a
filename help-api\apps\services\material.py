import os
import arrow
from bson import ObjectId
from fastapi import UploadFile
from apps.utils import singleton
from motor.core import AgnosticCollection
from apps.db import MongoDB
import apps.models.material as MaterialModel
from apps.utils import make_uuid
from apps.config import get_settings
from apps.models.common import ObjectIdStr
from apps.common import HttpResp, AppException


COLL_MATERIAL: AgnosticCollection = MongoDB.get_collection("material")
COLL_MATERIAL_CATE: AgnosticCollection = MongoDB.get_collection("material_cate")


async def get_next_id():
    """获取下一个现象ID"""
    cursor = COLL_MATERIAL_CATE.find().sort("_id", -1).limit(1)
    data = await cursor.to_list(None)
    if not data:
        return 1
    return int(data[0]["_id"]) + 1


async def get_cate_list(type: int = 1):
    # type 0-全部 1-图片 2-视频
    if type == 0:
        data = await COLL_MATERIAL_CATE.find().to_list(length=None)
    else:
        data = await COLL_MATERIAL_CATE.find({"type": type}).to_list(length=None)
    return [MaterialModel.MaterialCateOut(**item) for item in data]


async def add_cate(data: MaterialModel.MaterialCate):
    cate_json = data.model_dump()
    cate_json.update(
        {
            "_id": await get_next_id(),
            "create_time": arrow.utcnow().datetime,
            "update_time": arrow.utcnow().datetime,
        }
    )
    await COLL_MATERIAL_CATE.insert_one(cate_json)
    return {"msg": "添加成功"}


async def delete_cate(id: int):
    await COLL_MATERIAL_CATE.delete_one({"_id": id})
    return {"msg": "删除成功"}


async def update_cate(id: int, data: MaterialModel.MaterialCateEdit):
    update_data = {"name": data.name, "update_time": arrow.utcnow().datetime}
    result = await COLL_MATERIAL_CATE.update_one({"_id": id}, {"$set": update_data})
    return result.modified_count > 0


def get_material_url(filename: str, folder: str):
    ext = filename.split(".")[-1].lower()
    uuid = make_uuid()
    date = arrow.utcnow().format("YYYYMMDD")
    return folder + "/" + date + "/" + uuid + "." + ext


async def upload_material(file: UploadFile, cid: int = 0):
    # 获取文件扩展名
    file_ext = str(file.filename).split(".")[-1].lower()

    # 根据文件扩展名判断文件类型和目录
    file_type_str = get_file_type_by_extension(file_ext)

    # 设置目录和类型值（保持与原有数据库结构兼容）
    if file_type_str == "image":
        dir = "image"
        type = 1
    elif file_type_str == "video":
        dir = "video"
        type = 2
    elif file_type_str == "audio":
        dir = "audio"
        type = 3
    else:
        dir = "other"
        type = 4

    url = get_material_url(str(file.filename), dir)
    file_json = {
        "cid": cid,
        "type": type,
        "name": file.filename,
        "url": url,
        "size": file.size,
        "ext": file_ext,
        "create_time": arrow.utcnow().datetime,
    }

    file_location = os.path.join(get_settings().upload_directory, url).replace("\\", "/")
    file_path = os.path.dirname(file_location)

    if not os.path.exists(file_path):
        os.makedirs(file_path)
    with open(file_location, "wb") as f:
        f.write(await file.read())

    thumbnail = await get_thumbnail(url, type)
    file_json["thumbnail"] = thumbnail

    result = await COLL_MATERIAL.insert_one(file_json)
    return {"msg": "上传成功", "url": url, "id": str(result.inserted_id)}


async def get_thumbnail(url: str, type: int = 1):
    """生成缩略图

    Args:
        url: 文件相对路径
        type: 文件类型 (1=图片, 2=视频, 3=音频, 4=其他)
    """
    if not url:
        return ""

    file_path = os.path.join(get_settings().upload_directory, url).replace("\\", "/")
    thumbnail = f"{file_path}.thumbnail.jpg"

    # 如果缩略图已存在，直接返回
    if os.path.exists(thumbnail):
        return f"{url}.thumbnail.jpg"

    # 检查原文件是否存在
    if not os.path.exists(file_path):
        print(f"get_thumbnail error: 原文件不存在 {file_path}")
        return ""

    try:
        if type == 1:  # 图片
            success = await generate_image_thumbnail(file_path, thumbnail)
            if success:
                return f"{url}.thumbnail.jpg"
        elif type == 2:  # 视频
            success = await generate_video_thumbnail(file_path, thumbnail)
            if success:
                return f"{url}.thumbnail.jpg"
        elif type == 3:  # 音频
            # 音频文件暂时不生成缩略图，前端显示默认图标
            return ""
        else:  # 其他文件类型
            # 其他文件类型暂时不生成缩略图，前端显示默认图标
            return ""
    except Exception as e:
        print(f"get_thumbnail error: {e}")
        return ""

    return ""


async def generate_image_thumbnail(file_path: str, thumbnail_path: str) -> bool:
    """生成图片缩略图"""
    try:
        from PIL import Image

        with Image.open(file_path) as image:
            # 创建缩略图
            image.thumbnail((150, 150), Image.Resampling.LANCZOS)

            # 转换为RGB模式（确保JPEG兼容性）
            if image.mode in ('RGBA', 'LA', 'P'):
                # 创建白色背景
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'P':
                    image = image.convert('RGBA')
                background.paste(image, mask=image.split()[-1] if image.mode in ('RGBA', 'LA') else None)
                image = background
            elif image.mode != 'RGB':
                image = image.convert('RGB')

            # 保存缩略图
            image.save(thumbnail_path, format="JPEG", quality=85, optimize=True)
            return True

    except ImportError:
        print("get_thumbnail error: PIL (Pillow) 库未安装，无法生成图片缩略图")
        return False
    except Exception as e:
        print(f"generate_image_thumbnail error: {e}")
        return False


async def generate_video_thumbnail(file_path: str, thumbnail_path: str) -> bool:
    """生成视频缩略图"""
    try:
        import cv2

        cap = cv2.VideoCapture(file_path)
        if not cap.isOpened():
            print(f"generate_video_thumbnail error: 无法打开视频文件 {file_path}")
            return False

        # 尝试跳到视频的10%位置获取帧（避免黑屏）
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if frame_count > 10:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_count // 10)

        ret, frame = cap.read()
        cap.release()

        if not ret or frame is None:
            print(f"generate_video_thumbnail error: 无法读取视频帧 {file_path}")
            return False

        # 调整大小
        height, width = frame.shape[:2]
        if width > height:
            new_width = 150
            new_height = int(height * 150 / width)
        else:
            new_height = 150
            new_width = int(width * 150 / height)

        frame = cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)

        # 如果尺寸不是150x150，则居中裁剪或填充
        if new_width != 150 or new_height != 150:
            # 创建150x150的黑色背景
            thumbnail_frame = cv2.copyMakeBorder(
                frame,
                (150 - new_height) // 2,
                (150 - new_height + 1) // 2,
                (150 - new_width) // 2,
                (150 - new_width + 1) // 2,
                cv2.BORDER_CONSTANT,
                value=[0, 0, 0]
            )
        else:
            thumbnail_frame = frame

        # 保存缩略图
        success = cv2.imwrite(thumbnail_path, thumbnail_frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
        return success

    except ImportError:
        print("get_thumbnail error: OpenCV (opencv-python) 库未安装，无法生成视频缩略图")
        return False
    except Exception as e:
        print(f"generate_video_thumbnail error: {e}")
        return False


async def get_material_list(query: MaterialModel.MaterialQuery):
    filter = {"cid": query.cid, "type": query.type}
    if query.cid == None:
        filter = {"type": query.type}
    data = await COLL_MATERIAL.find(filter).to_list(length=None)
    count = await COLL_MATERIAL.count_documents(filter)
    return {"count": count, "lists": [MaterialModel.MaterialOut(**item) for item in data]}


def get_file_type_by_extension(ext: str) -> str:
    """根据文件扩展名判断文件类型"""
    ext = ext.lower()

    # 图片类型
    image_extensions = {'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico', 'tiff', 'tif'}
    # 视频类型
    video_extensions = {'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp', 'rm', 'rmvb'}
    # 音频类型
    audio_extensions = {'mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'}

    if ext in image_extensions:
        return "image"
    elif ext in video_extensions:
        return "video"
    elif ext in audio_extensions:
        return "audio"
    else:
        return "other"


def get_media_type_by_extension(ext: str) -> str:
    """根据文件扩展名获取对应的media_type"""
    ext = ext.lower()

    # 图片类型的media_type映射
    image_media_types = {
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'gif': 'image/gif',
        'bmp': 'image/bmp',
        'webp': 'image/webp',
        'svg': 'image/svg+xml',
        'ico': 'image/x-icon',
        'tiff': 'image/tiff',
        'tif': 'image/tiff'
    }

    # 视频类型的media_type映射
    video_media_types = {
        'mp4': 'video/mp4',
        'avi': 'video/x-msvideo',
        'mov': 'video/quicktime',
        'wmv': 'video/x-ms-wmv',
        'flv': 'video/x-flv',
        'webm': 'video/webm',
        'mkv': 'video/x-matroska',
        '3gp': 'video/3gpp',
        'rm': 'application/vnd.rn-realmedia',
        'rmvb': 'application/vnd.rn-realmedia-vbr'
    }

    # 音频类型的media_type映射
    audio_media_types = {
        'mp3': 'audio/mpeg',
        'wav': 'audio/wav',
        'flac': 'audio/flac',
        'aac': 'audio/aac',
        'ogg': 'audio/ogg',
        'wma': 'audio/x-ms-wma',
        'm4a': 'audio/mp4'
    }

    if ext in image_media_types:
        return image_media_types[ext]
    elif ext in video_media_types:
        return video_media_types[ext]
    elif ext in audio_media_types:
        return audio_media_types[ext]
    else:
        return 'application/octet-stream'


async def get_material_data_by_id(material_id: ObjectIdStr, request_type: str = ""):
    """根据素材ID获取素材数据

    Args:
        material_id: 素材ID
        request_type: 请求类型，"cover"表示获取缩略图，其他表示获取原文件

    Returns:
        tuple: (文件路径, 文件类型, media_type)
    """
    material_data = await COLL_MATERIAL.find_one({"_id": ObjectId(material_id)})
    if material_data is None:
        raise AppException(HttpResp.VIDEO_NOT_FOUND, "material info not found")

    # 获取文件扩展名
    file_ext = material_data.get("ext", "").lower()

    if request_type == "cover":
        # 返回缩略图
        file_path = os.path.join(get_settings().upload_directory, material_data["thumbnail"]).replace("\\", "/")
        return file_path, "image", "image/jpeg"  # 缩略图统一为jpeg格式
    else:
        # 返回原文件
        file_path = os.path.join(get_settings().upload_directory, material_data["url"]).replace("\\", "/")
        file_type = get_file_type_by_extension(file_ext)
        media_type = get_media_type_by_extension(file_ext)
        return file_path, file_type, media_type


async def delete_material(material_ids: list[ObjectIdStr]):
    res = await COLL_MATERIAL.delete_many({"_id": {"$in": material_ids}})
    if res.deleted_count > 0:
        return {"msg": "删除成功"}
    else:
        return {"msg": "删除失败"}