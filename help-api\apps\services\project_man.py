from typing import Dict, Any
from cgi import print_arguments
from itertools import count
import arrow
from bson import ObjectId
from apps.db.mongo_db import MongoDB
import apps.models.user as UserModel
import apps.models.project_man as ProjectModel
import apps.services.project_man as ProjectService
import apps.services.role as RoleService
from pymongo.errors import DuplicateKeyError
from apps.common import AppException, HttpResp
from collections import Counter
from .metadata import get_metadata
from fastapi import Request
import httpx


COLL_PROJECT = MongoDB.get_collection("project_man")


async def project_create(project: ProjectModel.ProjectCreate):
    """创建项目"""
    project_dict = project.model_dump()
    project_dict.update(
        {
            "prj_status": 0,  # 项目状态 0：未开始 1：已完成  2：生产中 3：测试中 4:已发货 5：部署中 6:验收中 99：有工单
            "is_public_network": False,  # 是否公网，默认否
            "is_oms": False,  # 是否接入管理平台，默认否
            "is_delete": False,  # 是否删除
            "prj_position": [116.454541, 39.92],  # 项目位置
            "prj_address": "北京市朝阳区呼家楼街道燕子胡同朝外SOHO-A座",  # 项目地址
            "devices_num": 0,  # 设备数量
            "work_order_num": 0,  # 工单数量
            "completion_rate": 0,  # 完成进度
            "prj_progress": [
                {
                    "content": "项目创建",
                    "timestamp": arrow.utcnow().datetime,
                    "progress_type": "primary",
                    "hollow": False,
                },
            ],  # 项目时间线，项目的生命周期，从开始到完成
            "installer": [  # 现场工程师，根据车辆分配自动生成
                {
                    "name": "张三",  # 工程师姓名
                    "user_id": "1234",  # 工程师用户ID
                }
            ],
            "salesperson": [],  # 销售伙伴
            "vehicle": [],  # 车辆信息
            "console": [],  # 控制台信息
            "work_order": [],  # 工单信息
            "create_time": arrow.utcnow().datetime,  # 创建时间
            "update_time": arrow.utcnow().datetime,  # 更新时间
        }
    )
    # {
    #     "name": "卡特305",  # 车辆名称
    #     "type": 1,  # 车辆类型
    #     "sn": "",  # 工控机唯一ID
    #     "smart_feature": [],  # 智能化功能
    #     "installer_name": "",  # 安装工程师姓名
    #     "installer_id": "",  # 安装工程师ID
    #     "status": 1,  # 车辆状态 0：未开始 1：已完成 2：进行中
    #     "guide_id": "1234",  # 指导ID
    #     "node_completed": [],  # 已完成的节点
    #     "guide_timeline": [  # 指导时间线
    #         {
    #             "id": 1,  # 时间线ID
    #             "time": "2024-12-12 12:12:12",  # 时间
    #             "content": "",  # 内容
    #             "user": "",  # 用户
    #             "user_id": "",  # 用户ID
    #         }
    #     ],
    #     "completion_rate": 20,  # 完成率
    #     "vehicle_id": "",  # 车辆ID
    # }
    res = await COLL_PROJECT.insert_one(project_dict)
    return {"msg": "项目创建成功", "id": str(res.inserted_id)}


async def project_list(query: ProjectModel.ProjectQuery):
    """获取项目列表"""
    filter = {"prj_name": query.prj_name, "is_delete": False}
    if query.prj_name == None:
        filter = {"is_delete": False}
    results = await COLL_PROJECT.find(filter).to_list(length=None)
    count = await COLL_PROJECT.count_documents(filter)
    return {"count": count, "lists": [ProjectModel.ProjectOut(**item) for item in results]}


async def project_detail(project_id: str):
    """获取项目详情"""
    project = await COLL_PROJECT.find_one({"_id": ObjectId(project_id)})
    if project is None:
        raise AppException(HttpResp.PROJECT_NOT_FOUND)

    project_info = ProjectModel.ProjectDetailOut(**project)

    # 获取车辆类型字典
    vehicle_type_res = await get_metadata("vehicle_type")

    # 将列表转换为字典，key为flag，value为name
    vehicle_type_dict = {item["flag"]: item["name"] for item in vehicle_type_res.value}

    # 将车辆类型ID转换为名称
    for vehicle in project_info.vehicle:
        vehicle["vehicle_type_name"] = vehicle_type_dict[vehicle["vehicle_type"]]

    # 使用Counter统计车辆类型
    vehicle_counts = Counter(vehicle["vehicle_type_name"] for vehicle in project_info.vehicle)

    # 统计车辆安装人员
    installer_list = []
    for vehicle in project_info.vehicle:
        for installer in vehicle["installer"]:
            installer_list.append(installer["nickname"])

    installer_list = list(set(installer_list))
    project_info.installer_info = "，".join(installer_list)

    # 将所有类型的统计信息组合成一个字符串
    count_devices_info = []
    for vehicle_type, count in vehicle_counts.items():
        count_devices_info.append(f"{count}台{vehicle_type}")

    # 用换行符连接所有统计信息
    project_info.devices_info = "，".join(count_devices_info)

    return project_info


async def project_delete(project_id: str):
    """删除项目"""
    await COLL_PROJECT.update_one({"_id": ObjectId(project_id)}, {"$set": {"is_delete": True}})
    return {"msg": "项目删除成功"}


async def project_base_update(project_id: str, base: ProjectModel.ProjectBaseUpdate):
    """更新项目基本信息"""
    await COLL_PROJECT.update_one({"_id": ObjectId(project_id)}, {"$set": base.model_dump()})
    return {"msg": "项目基本信息更新成功"}


async def project_map_update(project_id: str, map_info: ProjectModel.ProjectMapUpdate):
    """更新项目地图信息"""
    await COLL_PROJECT.update_one({"_id": ObjectId(project_id)}, {"$set": map_info.model_dump()})
    return {"msg": "项目地图信息更新成功"}


async def project_progress_update(project_id: str, progress: ProjectModel.ProjectProgressUpdate):
    """更新项目进度信息"""
    progress = progress.model_dump()
    progress.update(
        {
            "timestamp": arrow.utcnow().datetime,
        }
    )
    await COLL_PROJECT.update_one({"_id": ObjectId(project_id)}, {"$push": {"prj_progress": progress}})

    if progress['is_change_status'] and progress['change_status']:
        await COLL_PROJECT.update_one({"_id": ObjectId(project_id)}, {"$set": {"prj_status": progress['change_status']}})

    return {"msg": "项目进度信息更新成功"}


async def project_vehicle_add(project_id: str, vehicle: ProjectModel.ProjectVehicleAdd):
    """添加车辆"""
    vehicle = vehicle.model_dump()
    vehicle.update(
        {
            "vehicle_id": str(ObjectId()),
            "create_time": arrow.utcnow().datetime,
            "update_time": arrow.utcnow().datetime,
        }
    )
    await COLL_PROJECT.update_one({"_id": ObjectId(project_id)}, {"$push": {"vehicle": vehicle}})
    return {"msg": "车辆添加成功"}


async def project_vehicle_update(project_id: str, vehicle_id: str, vehicle: ProjectModel.ProjectVehicleAdd):
    """更新车辆信息"""
    vehicle = vehicle.model_dump()
    vehicle.update(
        {
            "update_time": arrow.utcnow().datetime,
        }
    )
    await COLL_PROJECT.update_one(
        {"_id": ObjectId(project_id), "vehicle.vehicle_id": vehicle_id}, {"$set": {"vehicle.$": vehicle}}
    )
    return {"msg": "车辆信息更新成功"}


async def project_vehicle_delete(project_id: str, vehicle_id: str):
    """删除车辆"""
    await COLL_PROJECT.update_one({"_id": ObjectId(project_id)}, {"$pull": {"vehicle": {"vehicle_id": vehicle_id}}})
    return {"msg": "车辆删除成功"}


async def project_console_add(project_id: str, console: ProjectModel.ProjectConsoleAdd):
    """添加控制台"""
    console = console.model_dump()
    console.update(
        {
            "console_id": str(ObjectId()),
            "create_time": arrow.utcnow().datetime,
            "update_time": arrow.utcnow().datetime,
        }
    )
    await COLL_PROJECT.update_one({"_id": ObjectId(project_id)}, {"$push": {"console": console}})
    return {"msg": "控制台添加成功"}


async def project_console_update(project_id: str, console_id: str, console: ProjectModel.ProjectConsoleAdd):
    """更新控制台信息"""
    console = console.model_dump()
    console.update(
        {
            "update_time": arrow.utcnow().datetime,
        }
    )
    await COLL_PROJECT.update_one(
        {"_id": ObjectId(project_id), "console.console_id": console_id}, {"$set": {"console.$": console}}
    )
    return {"msg": "控制台信息更新成功"}


async def project_console_delete(project_id: str, console_id: str):
    """删除控制台"""
    await COLL_PROJECT.update_one({"_id": ObjectId(project_id)}, {"$pull": {"console": {"console_id": console_id}}})
    return {"msg": "控制台删除成功"}


async def get_weather(request: Request):
    # 优先获取X-Forwarded-For头，这通常包含了原始客户端IP
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # X-Forwarded-For可能包含多个IP，第一个是原始客户端IP
        client_ip = forwarded_for.split(",")[0]
    else:
        # 如果没有X-Forwarded-For头，则使用直接连接的客户端IP
        client_ip = request.client.host

    client_ip = "***********"

    async with httpx.AsyncClient() as client:
        api_key = "SMR9sixvzKsme8EV7"
        url = (
            f"https://api.seniverse.com/v3/weather/now.json?key={api_key}&location={client_ip}&language=zh-Hans&unit=c"
        )
        res = await client.get(url)
        if res.status_code != 200:
            raise AppException(HttpResp.HLS_URL_NOT_FOUND)

        weather_data = res.json()
        result = weather_data["results"][0]

        # 获取天气信息
        temperature = result["now"]["temperature"]
        weather = result["now"]["text"]
        city = result["location"]["name"]

        # 根据温度生成温馨提示
        temp_tip = ""
        temp_int = int(temperature)
        if temp_int <= 5:
            temp_tip = "天气较冷，注意保暖"
        elif 5 < temp_int <= 15:
            temp_tip = "天气舒适，记得带件薄外套"
        elif 15 < temp_int <= 25:
            temp_tip = "心情也会因为好天气而变得愉悦"
        else:
            temp_tip = "天气有点热，注意防暑"

        welcome_msg = f"当前{city}天气{weather}，" f"气温{temperature}℃。{temp_tip}!"

    return {"msg": welcome_msg}


async def project_workbench_list(req: Request):
    """ 1.判断角色 2.根据角色返回，如果为现场工程师，返回自己所属项目，其余返回所有正在运行的项目"""
    """ 3.如果为现场工程师，先找出所有正在运行的项目，再找出现场工程师的项目 """
    """ 用户信息
        id=ObjectId('66de61c875f9d546876b21ab')
        role_ids=[ObjectId('677cf85be5e2dd7acc3ccf9d')]
        username='builderx'
        nickname='builderx'
        email='<EMAIL>' """
    user: UserModel.CacheInfo = req.state.user
    role_list = await RoleService.find_by_ids(user.role_ids)
    filter_d: Dict[Any, Any] = {"prj_status": {"$nin": [0, 1]}}

    if any(item['name'] == '现场工程师' for item in role_list):
        filter_d.update({"vehicle.installer": {
            "$elemMatch": {"username": user.username}
        }})
    else:
        print("不是现场工程师")

    results = await COLL_PROJECT.find(filter_d).to_list(length=None)
    return {"lists": [ProjectModel.ProjectOut(**item) for item in results]}


