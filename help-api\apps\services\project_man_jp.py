from typing import Dict, Any
from cgi import print_arguments
from itertools import count
import arrow
from bson import ObjectId
from apps.db.mongo_db import MongoDB
import apps.models.user as UserModel
import apps.models.project_man_jp as ProjectModel
import apps.services.project_man_jp as ProjectService
import apps.services.role as RoleService
from pymongo.errors import DuplicateKeyError
from apps.common import AppException, HttpResp
from collections import Counter
from .metadata import get_metadata
from fastapi import Request
import httpx


COLL_PROJECT = MongoDB.get_collection("project_man_jp")


async def project_create(req: Request, project: ProjectModel.ProjectCreate):
    """创建项目"""
    user_info = req.state._state
    project_dict = project.model_dump()
    project_dict.update(
        {
            "prj_status": 0,  # 项目状态 0：未开始 1：已完成  2：生产中 3：测试中 4:已发货 5：部署中 6:验收中 99：有工单
            "is_delete": False,  # 是否删除
            "create_username": user_info['user'].username,
            "contact_info": None,  # 联系方式
            "company_name": None,  # 单位名称
            "devices_info": None,  # 设备信息
            "vehicle": [],  # 车辆信息
            "console": [],  # 控制台信息
            "create_time": arrow.utcnow().datetime,  # 创建时间
            "update_time": arrow.utcnow().datetime,  # 更新时间
        }
    )
    res = await COLL_PROJECT.insert_one(project_dict)
    return {"msg": "项目创建成功", "id": str(res.inserted_id)}


async def project_list(req: Request, query: ProjectModel.ProjectQuery):
    """获取项目列表"""
    # 基础过滤条件
    filter_dict = {"is_delete": False}
    
    # 应用数据权限过滤
    user_info = req.state._state
    from apps.services.department import DepartmentService
    permission_filter = await DepartmentService().get_data_permission_filter(user_info)
    filter_dict.update(permission_filter)
    
    # 应用搜索条件
    if query.customer_name:
        search_conditions = []
        search_conditions.append({"customer_name": {"$regex": query.customer_name, "$options": "i"}})
        filter_dict["$or"] = search_conditions
    
    # 处理排序
    sort_options = {}
    if query.sort_field:
        sort_order = -1 if query.sort_order == "desc" else 1
        sort_options[query.sort_field] = sort_order
    
    cursor = COLL_PROJECT.find(filter_dict)
    if sort_options:
        cursor = cursor.sort(list(sort_options.items()))
    
    results = await cursor.to_list(length=None)
    count = await COLL_PROJECT.count_documents(filter_dict)
    return {"count": count, "lists": [ProjectModel.ProjectListOut(**item) for item in results]}


async def project_detail(project_id: str):
    """获取项目详情"""
    project = await COLL_PROJECT.find_one({"_id": ObjectId(project_id)})
    if project is None:
        raise AppException(HttpResp.PROJECT_NOT_FOUND)

    project_info = ProjectModel.ProjectDetailOut(**project)

    # 获取车辆类型字典
    vehicle_type_res = await get_metadata("vehicle_type")

    # 将列表转换为字典，key为flag，value为name
    vehicle_type_dict = {item["flag"]: item["name"] for item in vehicle_type_res.value}

    # 将车辆类型ID转换为名称
    for vehicle in project_info.vehicle:
        vehicle["vehicle_type_name"] = vehicle_type_dict[vehicle["vehicle_type"]]

    # 使用Counter统计车辆类型
    vehicle_counts = Counter(vehicle["vehicle_type_name"] for vehicle in project_info.vehicle)

    # 将所有类型的统计信息组合成一个字符串
    count_devices_info = []
    for vehicle_type, count in vehicle_counts.items():
        count_devices_info.append(f"{vehicle_type}{count}台")

    # 用换行符连接所有统计信息
    project_info.devices_info = "，".join(count_devices_info)

    return project_info


async def project_delete(project_id: str):
    """删除项目"""
    await COLL_PROJECT.update_one({"_id": ObjectId(project_id)}, {"$set": {"is_delete": True}})
    return {"msg": "项目删除成功"}


async def project_base_update(project_id: str, base: ProjectModel.ProjectBaseUpdate):
    """更新项目基本信息"""
    base_dict = base.model_dump()
    base_dict.update({"update_time": arrow.utcnow().datetime})
    await COLL_PROJECT.update_one({"_id": ObjectId(project_id)}, {"$set": base_dict})
    return {"msg": "项目基本信息更新成功"}

async def project_vehicle_add(project_id: str, vehicle: ProjectModel.ProjectVehicleAdd):
    """添加车辆"""
    vehicle = vehicle.model_dump()
    vehicle.update(
        {
            "vehicle_id": str(ObjectId()),
            "create_time": arrow.utcnow().datetime,
            "update_time": arrow.utcnow().datetime,
        }
    )
    await COLL_PROJECT.update_one({"_id": ObjectId(project_id)}, {"$push": {"vehicle": vehicle}})
    return {"msg": "车辆添加成功"}


async def project_vehicle_update(project_id: str, vehicle_id: str, vehicle: ProjectModel.ProjectVehicleAdd):
    """更新车辆信息"""
    vehicle = vehicle.model_dump()
    vehicle.update(
        {
            "update_time": arrow.utcnow().datetime,
        }
    )
    await COLL_PROJECT.update_one(
        {"_id": ObjectId(project_id), "vehicle.vehicle_id": vehicle_id}, {"$set": {"vehicle.$": vehicle}}
    )
    return {"msg": "车辆信息更新成功"}


async def project_vehicle_delete(project_id: str, vehicle_id: str):
    """删除车辆"""
    await COLL_PROJECT.update_one({"_id": ObjectId(project_id)}, {"$pull": {"vehicle": {"vehicle_id": vehicle_id}}})
    return {"msg": "车辆删除成功"}


async def project_console_add(project_id: str, console: ProjectModel.ProjectConsoleAdd):
    """添加控制台"""
    console = console.model_dump()
    console.update(
        {
            "console_id": str(ObjectId()),
            "create_time": arrow.utcnow().datetime,
            "update_time": arrow.utcnow().datetime,
        }
    )
    await COLL_PROJECT.update_one({"_id": ObjectId(project_id)}, {"$push": {"console": console}})
    return {"msg": "控制台添加成功"}


async def project_console_update(project_id: str, console_id: str, console: ProjectModel.ProjectConsoleAdd):
    """更新控制台信息"""
    console = console.model_dump()
    console.update(
        {
            "update_time": arrow.utcnow().datetime,
        }
    )
    await COLL_PROJECT.update_one(
        {"_id": ObjectId(project_id), "console.console_id": console_id}, {"$set": {"console.$": console}}
    )
    return {"msg": "控制台信息更新成功"}


async def project_console_delete(project_id: str, console_id: str):
    """删除控制台"""
    await COLL_PROJECT.update_one({"_id": ObjectId(project_id)}, {"$pull": {"console": {"console_id": console_id}}})
    return {"msg": "控制台删除成功"}


async def get_weather(request: Request):
    # 优先获取X-Forwarded-For头，这通常包含了原始客户端IP
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        # X-Forwarded-For可能包含多个IP，第一个是原始客户端IP
        client_ip = forwarded_for.split(",")[0]
    else:
        # 如果没有X-Forwarded-For头，则使用直接连接的客户端IP
        client_ip = request.client.host

    client_ip = "***********"

    async with httpx.AsyncClient() as client:
        api_key = "SMR9sixvzKsme8EV7"
        url = (
            f"https://api.seniverse.com/v3/weather/now.json?key={api_key}&location={client_ip}&language=zh-Hans&unit=c"
        )
        res = await client.get(url)
        if res.status_code != 200:
            raise AppException(HttpResp.HLS_URL_NOT_FOUND)

        weather_data = res.json()
        result = weather_data["results"][0]

        # 获取天气信息
        temperature = result["now"]["temperature"]
        weather = result["now"]["text"]
        city = result["location"]["name"]

        # 根据温度生成温馨提示
        temp_tip = ""
        temp_int = int(temperature)
        if temp_int <= 5:
            temp_tip = "天气较冷，注意保暖"
        elif 5 < temp_int <= 15:
            temp_tip = "天气舒适，记得带件薄外套"
        elif 15 < temp_int <= 25:
            temp_tip = "心情也会因为好天气而变得愉悦"
        else:
            temp_tip = "天气有点热，注意防暑"

        welcome_msg = f"当前{city}天气{weather}，" f"气温{temperature}℃。{temp_tip}!"

    return {"msg": welcome_msg}


async def project_workbench_list(req: Request):
    """ 1.判断角色 2.根据角色返回，如果为现场工程师，返回自己所属项目，其余返回所有正在运行的项目"""
    """ 3.如果为现场工程师，先找出所有正在运行的项目，再找出现场工程师的项目 """
    """ 用户信息
        id=ObjectId('66de61c875f9d546876b21ab')
        role_ids=[ObjectId('677cf85be5e2dd7acc3ccf9d')]
        username='builderx'
        nickname='builderx'
        email='<EMAIL>' """
    user: UserModel.CacheInfo = req.state.user
    role_list = await RoleService.find_by_ids(user.role_ids)
    filter_d: Dict[Any, Any] = {"prj_status": {"$nin": [0, 1]}}

    if any(item['name'] == '现场工程师' for item in role_list):
        filter_d.update({"vehicle.installer": {
            "$elemMatch": {"username": user.username}
        }})
    else:
        print("不是现场工程师")

    results = await COLL_PROJECT.find(filter_d).to_list(length=None)
    return {"lists": [ProjectModel.ProjectOut(**item) for item in results]}


