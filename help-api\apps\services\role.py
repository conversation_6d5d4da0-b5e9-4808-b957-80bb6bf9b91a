from typing import List, Union, Optional, Dict, Any

import arrow
from bson import ObjectId
from motor.core import AgnosticCollection
from pymongo.errors import DuplicateKeyError

from apps.db import MongoDB
from apps.common import HttpResp, AppException
from apps.models.common import PagingModel
from apps.models.permissions import RoleResourceFlag
import apps.models.user as UserModel


COLL: AgnosticCollection = MongoDB.get_collection("roles")


async def find_by_ids(role_ids: List[ObjectId]):
    return await COLL.find({"_id": {"$in": role_ids}}).to_list(None)


async def get_roles_menus(role_ids: List[ObjectId]):
    menu_ids = set()
    results = COLL.find({"_id": {"$in": role_ids}}, {"menus": 1})
    async for role in results:
        menu_ids.update(role.get("menus", []))
    return list(menu_ids)


async def role_create(role: UserModel.RoleCreate) -> dict:
    role_json = role.model_dump()
    role_json.update(
        {
            "create_time": arrow.utcnow().datetime,
            "update_time": arrow.utcnow().datetime,
        }
    )
    try:
        res = await COLL.insert_one(role_json)
    except DuplicateKeyError as exc:
        raise AppException(HttpResp.ROLE_NAME_REPEAT) from exc
    assert res.inserted_id, "Role info insert failed."
    role = UserModel.RoleOut.model_validate(role_json)
    res_data = role.model_dump()
    return res_data


async def role_list(page: Optional[PagingModel] = None):
    if page is None:
        cursor = COLL.find()
    else:
        skip = (page.page_no - 1) * page.page_size
        cursor = COLL.find().skip(skip).limit(page.page_size)
    total_count = await COLL.count_documents({})
    res_data = []
    async for role in cursor:
        role_obj = UserModel.RoleOut(**role)
        res_data.append(role_obj.model_dump())
    return {"count": total_count, "lists": res_data}


async def role_detail(rid: ObjectId):
    role = await COLL.find_one({"_id": rid})
    if not role:
        raise AppException(HttpResp.ROLE_NOT_EXIST)
    role_obj = UserModel.RoleOut(**role)
    return role_obj.model_dump()


async def role_delete(rid: ObjectId):
    res = await COLL.delete_one({"_id": rid})
    if res.deleted_count == 0:
        raise AppException(HttpResp.ROLE_NOT_EXIST)
    return {"msg": "success"}


async def role_update(
    role_id: ObjectId,
    update_data: Union[
        UserModel.RoleUpdate,
        UserModel.RoleUpdatePermission,
        UserModel.RoleUpdateMenus,
        UserModel.RoleUpdateResource,
    ],
):
    """更新角色信息"""
    data = update_data.model_dump()
    data.update({"update_time": arrow.utcnow().datetime})
    res = await COLL.update_one({"_id": role_id}, {"$set": data})
    if res.modified_count == 0:
        raise AppException(HttpResp.ROLE_NOT_EXIST)
    return {"msg": "success"}


async def get_permissions(role_ids: List[ObjectId]) -> List[UserModel.RoleOut]:
    """获取所有角色下的权限ID和资源ID"""
    filter_d = {"_id": {"$in": role_ids}}
    roles = COLL.find(
        filter_d,
        {
            "id_": 1,
            "name": 1,
            "apis": 1,
            "vehicles": 1,
            "opconsoles": 1,
            "resource_flag": 1,
        },
    )
    role_permissions = []
    async for role in roles:
        _r = UserModel.RoleOut(**role)
        role_permissions.append(_r)
    return role_permissions


async def is_have_permissions(
    role_ids: List[ObjectId],
    func_ids: List[UserModel.FuncId],
    res_flag: Optional[UserModel.RoleResourceFlag] = None,  # 资源角色标识
    opc_id: Optional[ObjectId] = None,  # 操作台ID
    vehicle_id: Optional[ObjectId] = None,  # 车辆ID
) -> bool:
    """检查角色列表中的角色是否有符合权限条件的角色"""

    # 超级管理员直接返回True
    if UserModel.SUPER_ADMIN_ROLE_ID in role_ids:
        return True

    # 组合查询条件
    filter_d: Dict[Any, Any] = {}
    filter_d["_id"] = {"$in": role_ids}
    if func_ids:
        filter_d["apis"] = {"$all": func_ids}
    if res_flag:
        filter_d["resource_flag"] = {"$gte": res_flag}
    if opc_id:
        filter_d["opconsoles"] = {"$all": [opc_id]}
    if vehicle_id:
        filter_d["vehicles"] = {"$all": [vehicle_id]}
    roles = await COLL.find_one(filter_d, {"id_": 1, "name": 1})
    if roles:
        return True
    return False


async def get_vehicles(role_ids: List[ObjectId], res_flag: int = RoleResourceFlag.VIEWER) -> List[ObjectId]:
    """获取角色下符合条件的车辆"""
    filter_d = {
        "_id": {"$in": role_ids},
        "resource_flag": {"$gte": res_flag},
    }
    roles = COLL.find(filter_d, {"vehicles": 1})
    vehicles = []
    async for role in roles:
        vehicles.extend(role.get("vehicles", []))
    return vehicles


async def get_opcs(role_ids: List[ObjectId], res_flag: int = RoleResourceFlag.VIEWER) -> List[ObjectId]:
    """获取角色下关联的操作台"""
    filter_d = {
        "_id": {"$in": role_ids},
        "resource_flag": {"$gte": res_flag},
    }
    roles = COLL.find(filter_d, {"opconsoles": 1})
    opcs: List[ObjectId] = []
    async for role in roles:
        opcs.extend(role.get("opconsoles", []))
    return opcs
