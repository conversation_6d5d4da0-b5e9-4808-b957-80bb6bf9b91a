"""
操作台 WebSocket 服务
"""

import time

import anyio
from bson import ObjectId
from fastapi import WebSocket

from apps.db import RedisDB
import apps.models.redis_key as RedisKey
import apps.models.websocket as WSModel
import apps.services.opconsole as OPCService
from apps.services.common import get_header_info
from .base import WebSockClient


class OPCSockClient(WebSockClient):
    """操作台WebSocket客户端链接"""

    def __init__(self, wsock: WebSocket, device_id: str, opc_id: ObjectId):
        super().__init__(wsock)
        self.odkey = RedisKey.OPCDevice(device_id)
        self.okey: RedisKey.OPC = RedisKey.OPC(opc_id)
        self.user_token_key = RedisKey.UserToken(wsock.state.user_token)

    async def init(self):
        await self.wsock.accept()
        await RedisDB.set(self.okey.online, 1)
        # await self.send_opc_info()

    async def clean(self):
        await RedisDB.delete(self.okey.online)  # 删除设备在线状态
        await RedisDB.hset(self.odkey.status, "offline_time", int(time.time()))

        # 清除所有房间消息订阅
        self.rooms.clear()
        # 解锁所有锁定的车辆
        locked_vehicles = await RedisDB.sget(self.okey.locked_vehicles)
        for v_id in locked_vehicles:
            await RedisDB.delete(RedisKey.Vehicle(v_id).locked_op_console)
        await RedisDB.delete(self.okey.locked_vehicles)

    async def send_opc_info(self):
        """发送操作台的一些信息"""
        msg = WSModel.OPCMsg(
            cmd=WSModel.OPCCmd.status_info.value,
            data={"_id": self.okey.id},
        )
        await self.send_msg(msg.model_dump_json())

    async def process_msg(self):
        """处理客户端发送的消息"""
        async with anyio.create_task_group() as tg:
            tg.start_soon(self.sub_room_msg, self.okey.id)
            async for msg in self.recv_socket_msg():
                if msg.room_id:
                    await self.send_room_msg(msg)
                    continue
                if msg.cmd == "ping":
                    await self.send_pong_msg(msg)
                elif msg.cmd.startswith("room."):
                    await self.room_switch(msg, tg)
            tg.cancel_scope.cancel()

    async def process_room_msg(self, msg: WSModel.MsgModel):
        """处理房间消息"""
        if msg.user == self.odkey.id:
            return
        await self.send_msg(msg.model_dump_json())

    async def send_room_msg(self, msg: WSModel.MsgModel):
        """发送消息到房间"""
        msg.user = self.okey.id
        # TODO: 这里需要判断是否有权限发送消息到目标房间
        assert msg.room_id
        await self.broadcast_room_msg(msg.room_id, msg)


async def process_opc(device_id: str, wsock: WebSocket):
    """处理操作台设备连接"""
    device_info = get_header_info(wsock)
    opc_info = await OPCService.get_opc_id_for_device(device_id, device_info)
    opc_id = opc_info["opc_id"]

    wsock.state.client_type = "opc"
    wsock.state.client_id = device_id
    wc = OPCSockClient(wsock, device_id, opc_id)
    try:
        await wc.init()
        await wc.process_msg()
    except Exception as e:  # pylint: disable=broad-except
        print(f"process op_console error:{e}")
    finally:
        await wc.clean()
    del wc
