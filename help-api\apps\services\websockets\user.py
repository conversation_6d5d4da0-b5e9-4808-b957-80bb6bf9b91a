"""
WebSocket 服务
"""

import anyio
from fastapi import WebSocket

import apps.models.websocket as WSModel
import apps.models.user as UserModel
from .base import WebSockClient


class UserSockClient(WebSockClient):
    """UserSocket 客户端,用于处理浏览器用户连接"""

    def __init__(self, wsock: WebSocket):
        super().__init__(wsock)
        self.user_info: UserModel.CacheInfo = wsock.state.user

    async def init(self):
        await self.wsock.accept()

    async def clean(self):
        self.rooms.clear()

    async def process_msg(self):
        async with anyio.create_task_group() as tg:
            tg.start_soon(self.sub_room_msg, self.user_info.username)
            async for msg in self.recv_socket_msg():
                if msg.room_id:
                    await self.send_room_msg(msg)
                    continue

                if msg.cmd == "ping":
                    await self.send_pong_msg(msg)
                elif msg.cmd.startswith("room."):
                    await self.room_switch(msg, tg)

            tg.cancel_scope.cancel()

    async def process_room_msg(self, msg: WSModel.MsgModel):
        """处理房间消息"""
        # 过滤自己的消息
        if msg.user == self.user_info.username:
            return
        await self.send_msg(msg.model_dump_json())

    async def send_room_msg(self, msg: WSModel.MsgModel):
        """发送消息到房间, 只能发送消息到本车辆房间"""
        msg.user = self.user_info.username
        # TODO: 这里需要判断是否有权限发送消息到目标房间
        assert msg.room_id
        await self.broadcast_room_msg(msg.room_id, msg)


async def process_user(wsock: WebSocket):
    """处理用户连接"""
    wsock.state.client_type = "user"
    wsock.state.client_id = wsock.state.user.username
    try:
        wc = UserSockClient(wsock)
        await wc.init()
        await wc.process_msg()
    except Exception as e:
        print(f"process user websocket error: {e}")
    finally:
        await wc.clean()
    del wc
