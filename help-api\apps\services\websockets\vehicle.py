"""
WebSocket 服务
"""

import json
import time
from typing import <PERSON>ple

import anyio
from bson import ObjectId
from fastapi import WebSocket

from apps.db import RedisDB
import apps.models.redis_key as RedisKey
import apps.models.websocket as WSModel
import apps.services.vehicles as VehicleService
from apps.services.common import get_header_info
from .base import WebSockClient


class VehicleSockClient(WebSockClient):
    """设备端客户端链接主要处理车端设备的链接"""

    def __init__(self, wsock: WebSocket, device_id: str, vehicle_id: str):
        super().__init__(wsock)
        self.vdkey = RedisKey.VehicleDevice(device_id)
        self.vkey: RedisKey.Vehicle = RedisKey.Vehicle(vehicle_id)

        self.room_id = f"room.{vehicle_id}"

    async def init(self):
        """设置连接上的时间"""
        await self.wsock.accept()
        await RedisDB.set(self.vkey.online_status, 1)
        await RedisDB.lpush(self.vkey.online_time, time.time())

    async def clean(self):
        self.rooms.clear()
        await RedisDB.delete(self.vkey.online_status)
        await RedisDB.lpush(self.vkey.offline_time, time.time())

    async def process_msg(self):
        """处理消息，房间消息和客户端消息"""
        async with anyio.create_task_group() as tg:
            # 订阅房间消息
            tg.start_soon(self.sub_room_msg, self.vkey.id)
            tg.start_soon(self.sub_room_msg, self.room_id)
            # 处理客户端消息
            async for msg in self.recv_socket_msg():
                # 更新在线状态
                await RedisDB.set(self.vkey.online_status, 1, timeout=30)
                # 需要发送到房间的消息
                if msg.room_id:
                    msg.user = self.vdkey.id
                    msg.room_id = self.room_id
                    await self.send_room_msg(msg)
                    continue
                if msg.cmd == "ping":
                    await self.send_pong_msg(msg)
                if msg.cmd == "status.ros_node":
                    pass
                elif msg.cmd == "status.vehicle.system":
                    pass
                elif msg.cmd == "sync.check":
                    await self.sync_data_check(msg.data)
                elif msg.cmd == "alarm.ros":
                    print("alarm message: ", msg.data)
                elif msg.cmd == "alarm.sys":
                    print("alarm message: ", msg.data)
                else:
                    print(f"unknown cmd: {msg.cmd}")
            # 关闭任务组
            tg.cancel_scope.cancel()

    async def send_room_msg(self, msg: WSModel.MsgModel):
        """发送消息到房间, 只能发送消息到本车辆房间"""
        msg.user = self.vdkey.id
        msg.room_id = self.room_id
        await self.broadcast_room_msg(msg.room_id, msg)

    async def process_room_msg(self, msg: WSModel.MsgModel):
        """处理房间消息, sub_room_msg 任务组中收到消息会调用此方法"""
        if msg.user == self.vdkey.id or msg.user == "data_manager":
            # 过滤自己，数据管理模块，发送的消息
            return
        # 其他消息转发到客户端
        await self.send_msg(msg.model_dump_json())

    async def update_status(self, status_key: str, data: dict):
        """更新客户端发送的状态信息到 Redis
        数据格式为
        """
        if status_key == "ros_node":
            await RedisDB.set(self.vkey.ros_nodes, json.dumps(data))

    async def sync_data_check(self, data):
        """同步检查, 对比所有数据，不同步则更新标志"""
        server_run_params_ = await VehicleService.get_run_params(ObjectId(self.vkey.id))
        assert server_run_params_, "device is not register"
        sync_code = WSModel.DeviceDataSyncCode.synced.value
        try:
            server_run_params = WSModel.DeviceRunData(**server_run_params_)
            device_run_params = WSModel.DeviceRunData(**data)
            if server_run_params != device_run_params:
                print("device data is not sync")
                sync_code = WSModel.DeviceDataSyncCode.nosync.value
            else:
                print("device data is syncd")
        except Exception as e:  # pylint: disable=broad-except
            print(f"device data is not sync: {e}")
            sync_code = WSModel.DeviceDataSyncCode.nosync.value
        await RedisDB.set(self.vkey.params_sync_status, sync_code)

    # async def room_switch(self, msg: WSModel.MsgModel):
    #     """车辆房间切换"""
    #     pass


async def vehicle_device_isregistered(wsock: WebSocket, device_id: str) -> Tuple[bool, str]:
    """是否已经注册"""

    # 缓存设备信息
    sys_info = get_header_info(wsock)
    vdkey = RedisKey.VehicleDevice(device_id)
    await RedisDB.hmset(vdkey.status, sys_info)

    # 检查设备是否已经注册
    index_ = await RedisDB.zkindex(vdkey.un_register_set, vdkey.id)
    if index_ is not None:
        return False, ""

    result = await VehicleService.find_by_device_id(vdkey.id)
    if result is None:
        await RedisDB.zadd(vdkey.un_register_set, {vdkey.id: int(time.time())})
        return False, ""

    # 缓存车辆Key信息
    vehicle_id = str(result["_id"])
    assert vehicle_id, "vehicle_id is None"
    return True, vehicle_id


async def process_vehicle(device_id: str, wsock: WebSocket):
    """处理车辆设备连接, 通过设备ID查询车辆ID"""
    is_ok, vehicle_id = await vehicle_device_isregistered(wsock, device_id)
    if not is_ok:
        print("device is not register")
        return

    wsock.state.client_type = "vehicle"
    wsock.state.client_id = device_id

    wc = VehicleSockClient(wsock, device_id, vehicle_id)
    try:
        await wc.init()
        await wc.process_msg()
    except Exception as e:  # pylint: disable=broad-except
        print(e)
    finally:
        await wc.clean()
    del wc
