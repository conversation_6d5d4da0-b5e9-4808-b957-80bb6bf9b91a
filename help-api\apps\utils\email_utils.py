import re
import email.utils
import yagmail
from typing import List, Dict, Any, Optional

from apps.db import MongoDB
from apps.utils import singleton


@singleton
class EmailService:
    """邮件服务工具类"""

    def __init__(self, _=None):
        self.sender_email = "<EMAIL>"
        self.sender_name = "BuilderX Service"
        self.password = "jjr6xJCyRya5jJKM"
        self.smtp_host = "smtp.feishu.cn"
        self.smtp_port = 465
        self.encoding = "utf-8"
        self.smtp_ssl = True

    async def get_department_user_emails(self, username: str) -> List[str]:
        """根据用户名获取该用户所在部门的所有用户邮箱
        
        Args:
            username: 用户名
            
        Returns:
            包含邮箱地址的列表
        """
        email_list = []
        # 获取工单创建者信息
        user_collection = MongoDB["users"]
        creator = await user_collection.find_one({"username": username})
        if creator:
            # 获取创建者的角色ID对应的部门ID
            role_collection = MongoDB["roles"]
            role_cursor = role_collection.find({"_id": {"$in": creator.get("role_ids", [])}})
            department_ids = []
            async for role in role_cursor:
                if role.get("department_id"):
                    department_ids.append(role.get("department_id"))
            
            if department_ids:
                # 获取这些部门下所有用户的邮箱
                pipeline = [
                    {"$match": {"role_ids": {"$exists": True}}},
                    {"$lookup": {
                        "from": "roles",
                        "localField": "role_ids",
                        "foreignField": "_id",
                        "as": "roles"
                    }},
                    {"$match": {"roles.department_id": {"$in": department_ids}}},
                    {"$project": {"email": 1, "_id": 0}},
                    {"$match": {"email": {"$exists": True, "$ne": None}}}
                ]
                email_cursor = user_collection.aggregate(pipeline)
                async for doc in email_cursor:
                    if doc.get("email"):
                        email_list.append(doc["email"])
        
        return email_list

    def get_completion_email_template(self, customer_name: str, device_control_name: str, device_vehicle_name: str, phenomenon: str, resolve: str) -> Dict[str, str]:
        """获取故障对应完成的邮件模板
        
        Args:
            customer_name: 客户名称
            device_control_name: 操作台名称
            device_vehicle_name: 设备端名称
            phenomenon: 现象描述
            resolve: 対応策
            
        Returns:
            包含subject和body的邮件数据字典
        """
        return {
            "subject": f"【故障対応完了のご報告】 BuilderX {customer_name}（※本メールは自動送信です）",
            "body": f"<html><head><style>body{{font-family:'Meiryo','MS PGothic',sans-serif;line-height:1.6;color:#333333;max-width:600px;margin:0 auto;padding:20px}}p{{margin:0 0 16px 0}}.footer{{margin-top:32px;padding-top:16px;border-top:1px solid #eeeeee;font-size:0.9em}}.company-info{{margin-top:8px}}</style></head><body><p>BuilderXの故障対応をご利用いただき、誠にありがとうございます。</p><p>このたびの障害対応が完了いたしましたのでご連絡申し上げます。</p><p>会社名：{customer_name}</p><p>操作台：{device_control_name}</p><p>設備端：{device_vehicle_name}</p><p>現象：{phenomenon}</p><p>対応策：{resolve}</p><p>ご利用中に生じたご不便につきまして、心よりお詫び申し上げます。</p><p>引き続き、BuilderXの遠隔操作システムをより快適にご利用いただけるようサービス改善に努めてまいります。</p><p>何かご不明な点がございましたら、本メールへの返信にてお問い合わせください。</p><div class=\"footer\"><div>BuilderX</div><div class=\"company-info\"><div><a href=\"https://www.jp.builderxrobotics.com/\">https://www.jp.builderxrobotics.com/</a></div><div>TEL:4001-133-112（平日10:30-19:30）</div></div></div></body></html>"
        }
    
    def get_processing_email_template(self, customer_name: str, device_control_name: str, device_vehicle_name: str, phenomenon: str) -> Dict[str, str]:
        """获取故障对应受理的邮件模板
        
        Args:
            customer_name: 客户名称
            device_control_name: 操作台名称
            device_vehicle_name: 设备端名称
            phenomenon: 现象描述
            
        Returns:
            包含subject和body的邮件数据字典
        """
        return {
            "subject": f"【故障対応受付のお知らせ】BuilderX {customer_name}（※本メールは自動送信です）",
            "body": f"<html><head><style>body{{font-family:'Meiryo','MS PGothic',sans-serif;line-height:1.6;color:#333333;max-width:600px;margin:0 auto;padding:20px}}p{{margin:0 0 16px 0}}.footer{{margin-top:32px;padding-top:16px;border-top:1px solid #eeeeee;font-size:0.9em}}.company-info{{margin-top:8px}}</style></head><body><p>現在、お問い合わせ内容を確認中です。</p><p>会社名：{customer_name}</p><p>操作台：{device_control_name}</p><p>設備端：{device_vehicle_name}</p><p>現象：{phenomenon}</p><p>担当者より改めてご連絡いたしますので、今しばらくお待ちくださいませ。</p><p>ご不明な点がございましたら、お気軽にお知らせください。</p><p>引き続きよろしくお願い申し上げます。</p><div class=\"footer\"><div>BuilderX</div><div class=\"company-info\"><div><a href=\"https://www.jp.builderxrobotics.com/\">https://www.jp.builderxrobotics.com/</a></div><div>TEL:4001-133-112（平日10:30-19:30）</div></div></div></body></html>"
        }

    async def send_email(self, email_list: List[str], email_data: Dict[str, Any]) -> bool:
        """发送邮件
        
        Args:
            email_list: 收件人邮箱列表
            email_data: 邮件内容，包含subject和body字段
            
        Returns:
            发送是否成功
        """
        # 清理和验证邮箱地址
        cleaned_email_list = []
        for email_addr in email_list:
            if not email_addr:
                print("跳过空邮箱地址")
                continue
                
            try:
                match = re.search(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', email_addr)
                if match:
                    cleaned_email = match.group(0)
                    formatted_email = email.utils.formataddr((None, cleaned_email))
                    cleaned_email_list.append(cleaned_email)
                    print(f"处理邮箱地址: {email_addr} -> {cleaned_email}")
                else:
                    print(f"无效的邮箱地址已跳过: {email_addr}")
            except Exception as e:
                print(f"处理邮箱地址时出错: {email_addr}, 错误: {str(e)}")

        if not cleaned_email_list:
            print("没有有效的邮箱地址，邮件发送已取消")
            return False

        try:
            yag = yagmail.SMTP(
                user=self.sender_email,
                password=self.password,
                host=self.smtp_host,
                port=self.smtp_port,
                encoding=self.encoding,
                smtp_ssl=self.smtp_ssl
            )
            
            print(f"准备发送邮件给: {cleaned_email_list}")
            
            yag.send(
                to=cleaned_email_list,
                subject=email_data['subject'],
                contents=email_data['body']
            )
            print("邮件发送成功")
            return True
        except Exception as e:
            print(f"邮件发送失败: {str(e)}")
            return False