from typing import List


__all__ = ["Tools"]


class Tools:
    """工具类"""

    @staticmethod
    def list_to_tree(arr: List[dict], id_: str, pid: str, child: str) -> List[dict]:
        dict_list = []
        id_dict_map = {i.get(id_): i for i in arr}
        for i in arr:
            p_node = id_dict_map.get(i.get(pid))
            if p_node:
                if child in p_node and p_node[child] is not None:
                    p_node[child].append(i)
                else:
                    p_node[child] = [i]
            else:
                dict_list.append(i)
        return dict_list
