--index-url https://pypi.tuna.tsinghua.edu.cn/simple

aiocsv==1.3.2 ; python_version >= "3.10" and python_version < "4.0"
aiohappyeyeballs==2.4.4 ; python_version >= "3.10" and python_version < "4.0"
aiohttp==3.11.11 ; python_version >= "3.10" and python_version < "4.0"
aiosignal==1.3.2 ; python_version >= "3.10" and python_version < "4.0"
annotated-types==0.7.0 ; python_version >= "3.10" and python_version < "4.0"
anyio==4.7.0 ; python_version >= "3.10" and python_version < "4.0"
arrow==1.3.0 ; python_version >= "3.10" and python_version < "4.0"
async-timeout==5.0.1 ; python_version >= "3.10" and python_full_version < "3.11.3"
attrs==24.3.0 ; python_version >= "3.10" and python_version < "4.0"
broadcaster==0.3.1 ; python_version >= "3.10" and python_version < "4.0"
cachetools==5.5.2 ; python_version >= "3.10" and python_version < "4.0"
certifi==2024.12.14 ; python_version >= "3.10" and python_version < "4.0"
charset-normalizer==3.4.1 ; python_version >= "3.10" and python_version < "4.0"
click==8.1.8 ; python_version >= "3.10" and python_version < "4.0"
colorama==0.4.6 ; python_version >= "3.10" and python_version < "4.0" and (platform_system == "Windows" or sys_platform == "win32")
cssselect==1.3.0 ; python_version >= "3.10" and python_version < "4.0"
cssutils==2.11.1 ; python_version >= "3.10" and python_version < "4.0"
dnspython==2.7.0 ; python_version >= "3.10" and python_version < "4.0"
email-validator==2.2.0 ; python_version >= "3.10" and python_version < "4.0"
exceptiongroup==1.2.2 ; python_version == "3.10"
fastapi-cli==0.0.7 ; python_version >= "3.10" and python_version < "4.0"
fastapi==0.115.6 ; python_version >= "3.10" and python_version < "4.0"
frozenlist==1.5.0 ; python_version >= "3.10" and python_version < "4.0"
h11==0.14.0 ; python_version >= "3.10" and python_version < "4.0"
httpcore==1.0.7 ; python_version >= "3.10" and python_version < "4.0"
httptools==0.6.4 ; python_version >= "3.10" and python_version < "4.0"
httpx==0.27.2 ; python_version >= "3.10" and python_version < "4.0"
idna==3.10 ; python_version >= "3.10" and python_version < "4.0"
influxdb-client==1.48.0 ; python_version >= "3.10" and python_version < "4.0"
jinja2==3.1.5 ; python_version >= "3.10" and python_version < "4.0"
lxml==5.3.2 ; python_version >= "3.10" and python_version < "4.0"
markdown-it-py==3.0.0 ; python_version >= "3.10" and python_version < "4.0"
markupsafe==3.0.2 ; python_version >= "3.10" and python_version < "4.0"
mdurl==0.1.2 ; python_version >= "3.10" and python_version < "4.0"
more-itertools==10.6.0 ; python_version >= "3.10" and python_version < "4.0"
motor==3.6.0 ; python_version >= "3.10" and python_version < "4.0"
multidict==6.1.0 ; python_version >= "3.10" and python_version < "4.0"
opencv-python==4.10.0.84 ; python_version >= "3.10" and python_version < "4.0"
pillow==10.4.0 ; python_version >= "3.10" and python_version < "4.0"
premailer==3.10.0 ; python_version >= "3.10" and python_version < "4.0"
propcache==0.2.1 ; python_version >= "3.10" and python_version < "4.0"
pydantic-core==2.27.2 ; python_version >= "3.10" and python_version < "4.0"
pydantic-settings==2.7.0 ; python_version >= "3.10" and python_version < "4.0"
pydantic==2.10.4 ; python_version >= "3.10" and python_version < "4.0"
pygments==2.18.0 ; python_version >= "3.10" and python_version < "4.0"
pymongo==4.9.2 ; python_version >= "3.10" and python_version < "4.0"
python-dateutil==2.9.0.post0 ; python_version >= "3.10" and python_version < "4.0"
python-dotenv==1.0.1 ; python_version >= "3.10" and python_version < "4.0"
python-multipart==0.0.16 ; python_version >= "3.10" and python_version < "4.0"
pyyaml==6.0.2 ; python_version >= "3.10" and python_version < "4.0"
reactivex==4.0.4 ; python_version >= "3.10" and python_version < "4.0"
redis==5.2.1 ; python_version >= "3.10" and python_version < "4.0"
requests==2.32.3 ; python_version >= "3.10" and python_version < "4.0"
rich-toolkit==0.12.0 ; python_version >= "3.10" and python_version < "4.0"
rich==13.9.4 ; python_version >= "3.10" and python_version < "4.0"
setuptools==75.6.0 ; python_version >= "3.10" and python_version < "4.0"
shellingham==1.5.4 ; python_version >= "3.10" and python_version < "4.0"
six==1.17.0 ; python_version >= "3.10" and python_version < "4.0"
sniffio==1.3.1 ; python_version >= "3.10" and python_version < "4.0"
starlette==0.41.3 ; python_version >= "3.10" and python_version < "4.0"
typer==0.15.1 ; python_version >= "3.10" and python_version < "4.0"
types-python-dateutil==2.9.0.20241206 ; python_version >= "3.10" and python_version < "4.0"
typing-extensions==4.12.2 ; python_version >= "3.10" and python_version < "4.0"
urllib3==2.3.0 ; python_version >= "3.10" and python_version < "4.0"
uvicorn==0.32.1 ; python_version >= "3.10" and python_version < "4.0"
uvloop==0.21.0 ; python_version >= "3.10" and python_version < "4.0" and sys_platform != "win32" and sys_platform != "cygwin" and platform_python_implementation != "PyPy"
watchfiles==1.0.3 ; python_version >= "3.10" and python_version < "4.0"
websockets==13.1 ; python_version >= "3.10" and python_version < "4.0"
yagmail==0.15.293 ; python_version >= "3.10" and python_version < "4.0"
yarl==1.18.3 ; python_version >= "3.10" and python_version < "4.0"
