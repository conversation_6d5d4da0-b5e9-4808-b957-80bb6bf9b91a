"""
    操作台增删修改查测试
"""

import json
import pytest
import apps.models.redis_key as Redis<PERSON>ey


@pytest.mark.dependency()
def test_add_opc(client, mdb, admin_headers):
    """新增操作台"""
    data = {"name": "pytest_opc_1", "type": "pytest_opc_type_1", "vehicle_ids": []}
    res = client.post("/op_consoles", json=data, headers=admin_headers)
    assert res.status_code == 200
    res_data = res.json()
    assert res_data["code"] == 200
    opc_data = mdb["op_consoles"].find_one({"name": "pytest_opc_1"})
    assert opc_data is not None


@pytest.mark.dependency(depends=["test_add_opc"])
def test_opc_bind_device(client, mdb, rdb, admin_headers):
    """操作台绑定设备"""
    opc_data = mdb["op_consoles"].find_one({"name": "pytest_opc_1"})
    assert opc_data is not None
    opc_id = str(opc_data["_id"])
    device_id = "111111111111111111111111"
    z_key = f"oms:{RedisKey.OPCDevice.un_register_set}"
    z_count = rdb.zadd(z_key, {device_id: 1})
    assert z_count > 0
    odkey = RedisKey.OPCDevice(device_id)
    h_count = rdb.hset(f"oms:{odkey.status}", "ros_node_status", "true")
    assert h_count > 0
    data = {"device_id": device_id}
    res = client.put(f"/op_consoles/{opc_id}/device_bind", json=data, headers=admin_headers)
    res_data = res.json()
    assert res_data["code"] == 200


@pytest.mark.dependency()
def test_opc_available_vehicles_list(client, mdb, admin_headers):
    """获取操作台绑定的车辆列表"""
    opc_data = mdb["op_consoles"].find_one({"name": "pytest_opc_1"})
    assert opc_data is not None
    opc_id = str(opc_data["_id"])
    res = client.get(f"/op_consoles/{opc_id}/available_vehicles", headers=admin_headers)
    res_data = res.json()
    assert res_data["code"] == 200
    # TODO 是否要判断一下这块返回的数据列表长度是否大于0


@pytest.mark.dependency()
def test_op_console_lock_vehicle(client, mdb, rdb, admin_headers):
    """锁定操作台与车"""
    opc_data = mdb["op_consoles"].find_one({"name": "pytest_opc_1"})
    assert opc_data is not None
    opc_id = str(opc_data["_id"])
    okey = RedisKey.OPC(opc_id)
    vehicle_id = "222222222222222222222222"
    count = rdb.sadd(f"oms:{okey.available_vehicles}", vehicle_id)
    assert count > 0
    res = client.post(f"/op_consoles/{opc_id}/lock_vehicle/{vehicle_id}", headers=admin_headers)
    res_data = res.json()
    assert res_data["code"] == 200


@pytest.mark.dependency(depends=["test_op_console_lock_vehicle"])
def test_get_locked_vehicles(client, mdb, admin_headers):
    """获取操作台锁定的车辆列表"""
    opc_data = mdb["op_consoles"].find_one({"name": "pytest_opc_1"})
    assert opc_data is not None
    opc_id = str(opc_data["_id"])
    res = client.get(f"/op_consoles/{opc_id}/available_vehicles", headers=admin_headers)
    res_data = res.json()
    assert res_data["code"] == 200
    # TODO 是否要判断一下这块返回的数据列表长度是否大于0


@pytest.mark.dependency(depends=["test_op_console_lock_vehicle"])
def test_op_console_unlock_vehicle(client, mdb, admin_headers):
    """解锁操作台与车"""
    opc_data = mdb["op_consoles"].find_one({"name": "pytest_opc_1"})
    assert opc_data is not None
    opc_id = str(opc_data["_id"])
    vehicle_id = "222222222222222222222222"
    res = client.post(f"/op_consoles/{opc_id}/unlock_vehicle/{vehicle_id}", headers=admin_headers)
    res_data = res.json()
    assert res_data["code"] == 200


@pytest.mark.dependency(depends=["test_add_opc"])
def test_update_opc(client, mdb, admin_headers):
    """更新操作台"""
    opc_data = mdb["op_consoles"].find_one({"name": "pytest_opc_1"})
    assert opc_data is not None
    opc_id = str(opc_data["_id"])
    data = {"name": "pytest_opc_updata_1", "type": "pytest_opc_type_updata_1", "vehicle_ids": []}
    res = client.put(f"/op_consoles/{opc_id}/", content=json.dumps(data), headers=admin_headers)
    res_data = res.json()
    assert res_data["code"] == 200
    opc_data = mdb["op_consoles"].find_one({"name": "pytest_opc_updata_1"})
    assert opc_data is not None


@pytest.mark.dependency(depends=["test_update_opc"])
def test_list_opc(client, admin_headers):
    """操作台列表"""
    res = client.get("/op_consoles", headers=admin_headers)
    res_data = res.json()
    assert res_data["code"] == 200
    assert len(res_data["data"]) > 0


@pytest.mark.dependency(depends=["test_list_opc"])
def test_del_opc(client, mdb, admin_headers):
    """删除操作台"""
    opc_data = mdb["op_consoles"].find_one({"name": "pytest_opc_updata_1"})
    assert opc_data is not None
    opc_id = str(opc_data["_id"])
    res = client.delete(f"/op_consoles/{opc_id}/", headers=admin_headers)
    res_data = res.json()
    assert res_data["code"] == 200
    opc_data = mdb["op_consoles"].find_one({"name": "pytest_opc_updata_1"})
    assert opc_data is None


@pytest.mark.dependency()
def test_un_register_list(client, admin_headers):
    """获取未注册设备列表"""
    res = client.get("/devices/op_consoles/un_register_list", headers=admin_headers)
    res_data = res.json()
    assert res_data["code"] == 200
    # assert len(res_data['list']) > 0
