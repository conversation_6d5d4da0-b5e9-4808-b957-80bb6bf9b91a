"""
    权限管理_角色增查改删测试
"""

import pytest


@pytest.mark.dependency()
def test_create_role(client, mdb, admin_headers):
    """新增角色"""
    data = {"id": "", "name": "pytest_role_1", "remark": "pytest_role_1", "sort": 0, "is_disable": 0, "menus": []}
    res = client.post("/user/role", json=data, headers=admin_headers)
    assert res.json()["code"] == 200
    role_data = mdb["roles"].find_one({"name": "pytest_role_1"})
    assert role_data is not None


@pytest.mark.dependency(depends=["test_create_role"])
def test_query_role(client, mdb, admin_headers):
    """查询角色"""
    res = client.get("/user/role", params={"page_no": 1, "page_size": 10}, headers=admin_headers)
    res_data = res.json()
    assert res_data["code"] == 200
    assert len(res_data["data"]) > 0


@pytest.mark.dependency(depends=["test_create_role"])
def test_detail_role(client, mdb, admin_headers):
    """查询角色详情"""
    role_data = mdb["roles"].find_one({"name": "pytest_role_1"})
    assert role_data is not None
    rid = str(role_data["_id"])
    res = client.get(f"/user/role/{rid}", headers=admin_headers)
    res_data = res.json()
    assert res_data["code"] == 200
    assert res_data["data"]["name"] == "pytest_role_1"


@pytest.mark.dependency(depends=["test_detail_role"])
def test_update_role(client, mdb, admin_headers):
    """更新角色"""
    role_data = mdb["roles"].find_one({"name": "pytest_role_1"})
    assert role_data is not None
    rid = str(role_data["_id"])
    data = {"name": "pytest_role_2", "remark": "pytest_role_2", "sort": 10, "is_disable": 0, "menus": []}
    res = client.put(f"/user/role/{rid}", json=data, headers=admin_headers)
    assert res.json()["code"] == 200
    role_data = mdb["roles"].find_one({"name": "pytest_role_2"})
    assert role_data is not None


@pytest.mark.dependency(depends=["test_update_role"])
def test_menu_role(client, mdb, admin_headers):
    """给角色分配菜单权限"""
    res = client.get("/user/menu", headers=admin_headers)
    res_data = res.json()
    assert res_data["code"] == 200
    assert len(res_data["data"]) > 0
    menu = res_data["data"][0]["id"]

    role_data = mdb["roles"].find_one({"name": "pytest_role_2"})
    assert role_data is not None
    rid = str(role_data["_id"])
    data = {"menus": [menu]}
    res = client.put(f"/user/role/{rid}/menu", json=data, headers=admin_headers)
    assert res.json()["code"] == 200
    role_data = mdb["roles"].find_one({"name": "pytest_role_2"})
    assert role_data is not None
    assert role_data["menus"] == [menu]


@pytest.mark.dependency(depends=["test_menu_role"])
def test_delete_role(client, mdb, admin_headers):
    """删除角色"""
    role_data = mdb["roles"].find_one({"name": "pytest_role_2"})
    assert role_data is not None
    rid = str(role_data["_id"])
    res = client.delete(f"/user/role/{rid}", headers=admin_headers)
    assert res.json()["code"] == 200
    role_data = mdb["roles"].find_one({"name": "pytest_role_2"})
    assert role_data is None
