"""
    测试流程
        增加一个未信任的设备
        查询未信任设备列表
        增加一个待绑定的车辆
        绑定设备和车辆
        查询绑定设备列表
        删除车辆
        删除设备
"""


def test_add_ros_node(client, admin_headers):
    pass


def test_update_ros_node(client, admin_headers):
    pass


def test_query_ros_node(client, admin_headers):
    pass


def test_delete_ros_node(client, admin_headers):
    pass


# def test_query_vehicle_device(client, admin_headers):
#     pass


# def test_delete_vehicle_device(client, admin_headers):
#     pass
