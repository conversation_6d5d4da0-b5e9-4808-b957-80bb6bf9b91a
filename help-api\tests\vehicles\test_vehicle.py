"""
    车辆增删修改查测试
"""

import time
import pytest
import apps.models.redis_key as RedisKey


TEST_DEVICE_ID = "1111111111111"


@pytest.fixture(scope="module")
def vd_key():
    yield RedisKey.VehicleDevice(TEST_DEVICE_ID)


@pytest.mark.dependency()
def test_add_vehicle(client, admin_headers, mdb):
    data = {"vehicle_name": "test_vehicle", "vehicle_type": 1, "description": "test vehicle"}
    res = client.post("/vehicles", json=data, headers=admin_headers)
    assert res.json()["code"] == 200
    vehicle = mdb["vehicles"].find_one({"vehicle_name": "test_vehicle"})
    assert vehicle is not None


@pytest.mark.dependency(depends=["test_add_vehicle"])
def test_update_vehicle(client, admin_headers, mdb):
    vehicle = mdb["vehicles"].find_one({"vehicle_name": "test_vehicle"})
    assert vehicle is not None
    v_id = vehicle["_id"]
    data = {
        "vehicle_name": "test_vehicle_changed",
        "vehicle_type": 1,
        "description": "test vehicle changed",
    }
    res = client.put(f"/vehicles/{v_id}", json=data, headers=admin_headers)
    assert res.json()["code"] == 200
    vehicle = mdb["vehicles"].find_one({"vehicle_name": "test_vehicle_changed"})
    assert vehicle is not None


@pytest.mark.dependency(depends=["test_update_vehicle"])
def test_query_vehicle(client, admin_headers):
    res = client.get("/vehicles", headers=admin_headers)
    assert res.json()["code"] == 200
    vehicle_list = res.json()["data"]["lists"]
    assert len(vehicle_list) > 0
    for i in vehicle_list:
        if i["vehicle_name"] == "test_vehicle_changed":
            assert True
            return
    assert False


@pytest.mark.dependency(depends=["test_query_vehicle"])
def test_delete_vehicle(client, admin_headers, mdb):
    vehicle = mdb["vehicles"].find_one({"vehicle_name": "test_vehicle_changed"})
    assert vehicle is not None
    v_id = vehicle["_id"]

    res = client.delete(f"/vehicles/{v_id}", headers=admin_headers)
    assert res.json()["code"] == 200

    res = client.get(f"/vehicles/{v_id}", headers=admin_headers)
    assert res.json()["code"] == 404


@pytest.mark.dependency(depends=["test_add_vehicle"])
def test_add_unregister_device(client, admin_headers, rdb, vd_key):
    """添加一个未信任测试设备"""
    device_info = {
        "ip": "127.0.0.1",
        "device_type": "miwen",
    }
    rdb.hset(f"oms:{vd_key.status}", mapping=device_info)
    rdb.zadd(f"oms:{vd_key.un_register_set}", {TEST_DEVICE_ID: int(time.time())})
    res = client.get("/devices/vehicles/un_register_list", headers=admin_headers)
    assert res.status_code == 200
    res_data = res.json()
    assert res_data["code"] == 200
    assert res_data["data"]["count"] > 0
    assert TEST_DEVICE_ID in str(res_data["data"]["lists"])


@pytest.mark.dependency(depends=["test_add_unregister_device"])
def test_bind_vehicle_device(client, admin_headers, mdb):
    # 添加一个车辆
    data = {"vehicle_name": "test_vehicle_bind", "vehicle_type": 1, "description": "test vehicle"}
    res = client.post("/vehicles", json=data, headers=admin_headers)
    assert res.json()["code"] == 200
    vehicle = mdb["vehicles"].find_one({"vehicle_name": "test_vehicle_bind"})
    assert vehicle is not None

    # 绑定设备
    vehicle_id = str(vehicle["_id"])
    req_data = {"device_id": TEST_DEVICE_ID}
    res = client.put(f"/vehicles/{vehicle_id}/device_bind", json=req_data, headers=admin_headers)
    assert res.json()["code"] == 200
    res = client.get("/devices/vehicles/un_register_list", headers=admin_headers)
    assert res.status_code == 200
    res_data = res.json()
    assert res_data["code"] == 200
    assert TEST_DEVICE_ID not in str(res_data["data"]["lists"])
