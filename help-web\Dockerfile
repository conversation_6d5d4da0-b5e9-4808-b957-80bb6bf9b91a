# node镜像
FROM hub.apps.builderx.com/node:20-alpine as build-stage

ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 维护者信息
LABEL author="Chen"

RUN echo "-------------------- web环境配置 --------------------"

# 指定接下来的工作路径为/app  - 类似于cd命令
WORKDIR /app
# 拷贝本机代码到app目录下
COPY . /
# 设置 node 阿里镜像
RUN npm config set registry https://registry.npmmirror.com/
# 安装依赖
RUN npm install
# 打包
RUN npm run build

RUN echo "🎉 打 🎉 包 🎉 成 🎉 功 🎉"

# nginx镜像
FROM hub.apps.builderx.com/nginx:1.26.2-alpine as production-stage

# 移除nginx容器的default.conf文件
RUN rm /etc/nginx/conf.d/default.conf
# 把主机的nginx.conf文件复制到nginx容器的/etc/nginx文件夹下
COPY /nginx/default.conf /etc/nginx/conf.d/
# 拷贝前端vue项目打包后生成的文件到nginx下运行
COPY --from=build-stage /dist /usr/share/nginx/html

# 暴露端口
EXPOSE 80

RUN echo "🎉 部 🎉 署 🎉 成 🎉 功 🎉"
