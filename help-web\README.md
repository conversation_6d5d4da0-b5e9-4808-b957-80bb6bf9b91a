#### 拓疆者智控平台

*   ✨ [在线预览](http://oms.apps.builderx.com/)

*   📦 [下载地址](https://e.gitee.com/builderxinc/repos/bux-server/bux-admin-web)

*   📖 [相关文档](https://www.feishu.cn/drive/folder/fldcn2Imdjvn4zCclWn9mAYnVph)
  
##### 项目介绍

BUX-ADMIN-WEB是智能管控中心的前端项目，基于Vue3+ElementPlus开发，主要包含以下功能：

*   设备管理：设备列表、设备添加、设备编辑、设备删除

*   素材中心：录像在线查看、录像下载、录像打包

*   数据总览：设备在线统计、设备告警统计、设备类型统计、设备区域统计

*   日志查看：操作日志、告警日志

*   轨迹回放：查看指定设备指定时间段的运行轨迹

*   安装包管理：安装包列表、安装包上传、安装包删除

*   用户管理：用户列表、用户添加、用户编辑、用户删除、用户重置密码、用户分配角色

*   角色管理：角色列表、角色添加、角色编辑、角色删除、角色分配权限

*   权限管理：权限列表、权限添加、权限编辑、权限删除


##### 使用说明

1.  安装依赖

    ```bash
    npm install
    ```

2.  启动项目

    ```bash
    npm run dev
    ```

3.  打包项目

    ```bash
    npm run build
    ```

##### Docker说明
    
1.  构建镜像(注意 .)

    ```bash
    docker build -t <image_name>:<tag> .
    ```

2.  运行容器

    ```bash
    # -p 指定外部端 8080 于容器内 80端口连接
    # -d 表示在后台运行
    docker run --name <container_name> -d -p 8080:80 <image_name>:<tag>
    ```

#### Git分支管理

*   `master` 主分支，用于发布正式版本，不能直接在该分支上开发

*   `develop` 开发分支，用于日常开发，不能直接在该分支上开发

*   每次提交代码前，先从`develop`分支拉取最新代码，再提交代码

*   每次提交代码时，必须填写提交信息，提交信息按照git提交规范提交，详见下方

#### Git提交规范

*   `feat` 添加新功能

*   `fix` 修复问题

*   `style` 代码格式化

*   `perf` 性能优化

*   `refactor` 重构

*   `revert` 撤消编辑

*   `test` 测试用例等

*   `docs` 文档注释等

*   `chore` 依赖更新/脚手架配置修改等

*   `workflow` 工作流程改进

*   `types` 类型文件修改
