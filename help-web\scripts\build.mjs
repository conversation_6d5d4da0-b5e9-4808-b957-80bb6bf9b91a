import { execaCommand, execa } from "execa";
import path from "path";
import fsExtra from "fs-extra";
const { existsSync, remove, copy, ensureDir } = fsExtra;
const cwd = process.cwd();
//打包发布路径，谨慎改动
const releaseRelativePath = "../frontend";
const distPath = path.resolve(cwd, "dist");
const releasePath = path.resolve(cwd, releaseRelativePath);

// 服务器相关配置
const serverHost = "**************";
const serverUsername = "root";
// const serverDestination = "/opt/builderx-oms/www";
const serverDestination = "/opt/1panel/apps/openresty/openresty/www/sites/test.oms.apps.builderx.com/index";

// "build": "node ./scripts/build.mjs",

async function build() {
  console.log('产物构建中');
  await execaCommand("vite build", { stdio: "inherit", encoding: "utf-8", cwd });
  console.log('产物构建完成');
  if (existsSync(releasePath)) {
    await remove(releasePath);
  }
  try {
    await copyFile(distPath, releasePath);
    await deleteExistingFiles();
    setTimeout(() => {
      pushToServer();
    }, 2000);
  } catch (error) {
    console.log(`\n ${error}`);
  }
}

function copyFile(sourceDir, targetDir) {
  return new Promise((resolve, reject) => {
    copy(sourceDir, targetDir, (err) => {
      console.log(`文件正在复制 ==> ${releaseRelativePath}`);
      if (err) {
        reject(err);
      } else {
        console.log(`文件复制完成 ==> ${releaseRelativePath}`);
        resolve();
      }
    });
  });
}

async function deleteExistingFiles() {
  try {
    const assetsDir = path.join(serverDestination, "assets").replace(/\\/g, "/");
    const assetsExists = await execa("ssh", [
      `${serverUsername}@${serverHost}`,
      `[ -d ${assetsDir} ] && echo "true" || echo "false"`,
    ]);

    if (assetsExists.stdout === "true") {
      setTimeout(() => {
        execa("ssh", [`${serverUsername}@${serverHost}`, `rm -f ${assetsDir}/*`]);
        console.log("已删除无用资源");
      }, 1000);
    }
  } catch (error) {
    console.error("删除服务器文件失败:", error);
  }
}

async function pushToServer() {
  console.log("文件开始上传");
  try {
    await execa("scp", ["-r", `${releasePath}/*`, `${serverUsername}@${serverHost}:${serverDestination}`]);
    console.log("文件已推送到服务器");
    console.log(`部署完成🎉`);
  } catch (error) {
    console.error("推送文件到服务器失败:", error);
  }
}

build();
