import request from "@/utils/request";

// 操作列表
export function operationList(params: any) {
  return request.get({ url: "/v1/customer/operation", params });
}
// 创建操作
export function operationAdd(params: any) {
  return request.post({ url: "/v1/customer/operation", params });
}
// 删除操作
export function operationDelete(id: any) {
  return request.delete({ url: `/v1/customer/operation/${id}` });
}
// 编辑操作
export function operationEdit(params: any) {
  return request.put({ url: `/v1/customer/operation/${params.id}`, params });
}

// 现象列表
export function phenomenonList(params: any) {
  return request.get({ url: "/v1/customer/phenomenon", params });
}
// 创建现象
export function phenomenonAdd(params: any) {
  return request.post({ url: "/v1/customer/phenomenon", params });
}
// 删除现象
export function phenomenonDelete(id: any) {
  return request.delete({ url: `/v1/customer/phenomenon/${id}` });
}
// 编辑现象
export function phenomenonEdit(params: any) {
  return request.put({ url: `/v1/customer/phenomenon/${params.id}`, params });
}

// 用户列表
export function consumerAdd(params: any) {
  return request.post({ url: "/v1/customer/consumer", params });
}
// 用户列表
export function consumerList(params: any) {
  return request.get({ url: "/v1/customer/consumer", params });
}
// 用户编辑
export function consumerEdit(params: any) {
  return request.put({ url: `/v1/customer/consumer/${params.id}`, params });
}
// 用户删除
export function consumerDelete(id: any) {
  return request.delete({ url: `/v1/customer/consumer/${id}` });
}

// 工单列表
export function orderAdd(params: any) {
  return request.post({ url: "/v1/customer/order", params });
}
// 工单列表
export function orderList(params: any) {
  return request.get({ url: "/v1/customer/order", params });
}
// 工单编辑
export function orderEdit(params: any) {
  return request.put({ url: `/v1/customer/order/${params.id}`, params });
}
// 工单删除
export function orderDelete(id: any) {
  return request.delete({ url: `/v1/customer/order/${id}` });
}

// 同步数据
export function syncDataApi() {
  return request.get({ url: "/v1/customer/sync_data" });
}
