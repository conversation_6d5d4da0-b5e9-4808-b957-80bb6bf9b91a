import request from '@/utils/request';

/**
 * 获取部门列表
 */
export function departmentLists(params: any) {
  return request.get({ url: '/v1/department', params });
}

/**
 * 获取部门树形结构
 */
export function departmentTree() {
  return request.get({ url: '/v1/department/tree' });
}

/**
 * 获取部门详情
 */
export function departmentDetail(id: string) {
  return request.get({ url: `/v1/department/${id}` });
}

/**
 * 新增部门
 */
export function departmentAdd(params: any) {
  return request.post({ url: '/v1/department', params });
}

/**
 * 修改部门
 */
export function departmentEdit(params: any) {
  return request.put({ url: `/v1/department/${params.id}`, params });
}

/**
 * 删除部门
 */
export function departmentDelete(id: string) {
  return request.delete({ url: `/v1/department/${id}` });
}