import request from "@/utils/request";

// 菜单列表
export function menuLists(params?: Record<string, any>) {
  return request.get({ url: "/v1/user/menu", params, headers: { "X-Variable-Naming-Style": "camelCase" } });
}

// 添加菜单
export function menuAdd(params: Record<string, any>) {
  return request.post({ url: "/v1/user/menu", params }, { isSnakeCase: true });
}

// 编辑菜单
export function menuEdit(params: Record<string, any>) {
  return request.put({ url: "/v1/user/menu", params }, { isSnakeCase: true });
}

// 删除菜单
export function menuDelete(params: Record<string, any>) {
  return request.delete({ url: "/v1/user/menu", params });
}

// 菜单详情
export function menuDetail(params: Record<string, any>) {
  return request.get(
    { url: "/v1/user/menu/detail", params, headers: { "X-Variable-Naming-Style": "camelCase" } },
    { isParamsToData: false }
  );
}

// 下载菜单
export function menuDownload() {
  return request.get({ url: "/v1/user/menu/export" });
}

// 上传菜单
export function menuUpload(params: Record<string, any>) {
  return request.post({ url: "/v1/user/menu/import", params });
}
