import request from "@/utils/request";

// 新建项目
export function projectAdd(params: Record<string, any>) {
  return request.post({ url: "/v1/project", params });
}

// 项目列表
export function projectList(params: Record<string, any>) {
  return request.get({ url: "/v1/project", params });
}

// 删除项目
export function projectDelete(id: string) {
  return request.delete({ url: `/v1/project/${id}` });
}

// 编辑项目
export function projectEdit(params: Record<string, any>) {
  return request.put({ url: `/v1/project/${params.id}`, params });
}

// 项目详情
export function projectDetail(id: string) {
  return request.get({ url: `/v1/project/${id}` });
}

// 项目基本信息编辑
export function projectBaseEdit(params: Record<string, any>) {
  return request.put({ url: `/v1/project/${params.id}/base`, params });
}

// 项目位置信息编辑
export function projectLocationEdit(params: Record<string, any>) {
  return request.put({ url: `/v1/project/${params.id}/location`, params });
}

// 更新项目进度
export function projectProgressEdit(params: Record<string, any>) {
  return request.put({ url: `/v1/project/${params.id}/progress`, params });
}

// 项目添加车辆
export function projectAddVehicle(params: Record<string, any>) {
  return request.post({ url: `/v1/project/${params.id}/vehicle`, params });
}

// 项目编辑车辆
export function projectEditVehicle(params: Record<string, any>) {
  return request.put({ url: `/v1/project/${params.id}/vehicle/${params.vehicle_id}`, params });
}

// 项目删除车辆
export function projectDeleteVehicle(params: Record<string, any>) {
  return request.delete({ url: `/v1/project/${params.id}/vehicle/${params.vehicle_id}` });
}

// 项目添加操作台
export function projectAddConsole(params: Record<string, any>) {
  return request.post({ url: `/v1/project/${params.id}/console`, params });
}

// 项目编辑操作台
export function projectEditConsole(params: Record<string, any>) {
  return request.put({ url: `/v1/project/${params.id}/console/${params.console_id}`, params });
}

// 项目删除操作台
export function projectDeleteConsole(params: Record<string, any>) {
  return request.delete({ url: `/v1/project/${params.id}/console/${params.console_id}` });
}

// 天气预报
export function weatherForecast() {
  return request.get({ url: `/v1/project/weather` });
}

// 工作台项目列表
export function workbenchProjectList() {
  return request.get({ url: `/v1/project/workbench` });
}
