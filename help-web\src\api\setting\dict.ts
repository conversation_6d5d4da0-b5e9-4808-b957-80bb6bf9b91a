import request from "@/utils/request";

// 元数据列表
export function metaDataList() {
  return request.get({ url: `/v1/metadata` });
}

// 元数据获取
export function metaDataGet(key: string) {
  return request.get({ url: `/v1/metadata/${key}` });
}

// 添加元数据
export function metaDataAdd(params: Record<string, any>) {
  return request.post({ url: `/v1/metadata`, params });
}

// 编辑元数据
export function metaDataEdit(params: Record<string, any>) {
  return request.put({ url: `/v1/metadata/${params.key}`, params });
}

// 删除元数据
export function metaDataDelete(key: string) {
  return request.delete({ url: `/v1/metadata/${key}` });
}
