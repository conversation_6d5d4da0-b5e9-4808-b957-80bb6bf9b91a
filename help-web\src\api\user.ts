import config from "@/config";
import request from "@/utils/request";

// 登录
export function login(params: Record<string, any>) {
  return request.post({ url: "/v1/user/login", params: { ...params, terminal: config.terminal } });
}

// 退出登录
export function logout() {
  return request.get({ url: "/v1/user/logout" });
}

// 用户信息
export function getUserInfo() {
  return request.get({ url: "/v1/user/info" });
}

// 菜单路由
export function getMenu() {
  return request.get({ url: "/v1/user/menu/route", headers: { "X-Variable-Naming-Style": "camelCase" } });
}

// 编辑人员信息
export function setUserInfo(params: Record<string, any>) {
  return request.put({ url: "/v1/user/me", params }, { isSnakeCase: true });
}

// 编辑人员信息
export function setUserPassword(params: Record<string, any>) {
  return request.put({ url: "/v1/user/me/password", params }, { isSnakeCase: true });
}

// 请求超级接口
export function superApi() {
  return request.post({ url: "/init_data" }, { urlPrefix: "/super_interface" });
}
