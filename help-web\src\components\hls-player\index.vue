<template>
  <video ref="video" :controls="isControls" muted :style="{ width: width, height: height, 'object-fit': fit }"></video>
</template>

<script setup lang="ts">
import HlS from "hls.js";
import { ref, reactive, shallowRef, onMounted, onBeforeUnmount } from "vue";

const props = defineProps({
  url: {
    type: String,
    required: true,
  },
  token: {
    type: String,
    required: true,
  },
  width: {
    type: String,
    default: "100%",
  },
  height: {
    type: String,
    default: "100%",
  },
  isControls: {
    type: Boolean,
    default: false,
  },
  fit: {
    type: String,
    default: "contain",
  },
});

let hls: any = null;
const video: any = ref(null);

const close = () => {
  if (hls) {
    hls.destroy();
    hls = null;
  }
};

const play = () => {
  close();

  if (HlS.isSupported()) {
    hls = new HlS({
      xhrSetup: (xhr) => {
        xhr.setRequestHeader("Authorization", props.token);
      },
    });
    hls.loadSource(props.url);
    hls.attachMedia(video.value);
    hls.on(HlS.Events.MANIFEST_PARSED, () => {
      video.value.play();
    });
  } else if (video.value.canPlayType("application/vnd.apple.mpegurl")) {
    // 对于支持原生HLS的浏览器（如Safari）
    video.value.src = props.url;
    video.value.addEventListener("loadedmetadata", () => {
      video.value.play();
    });
  }
};

onMounted(() => {
  // play();
  console.log(props.url);
});

onBeforeUnmount(() => {
  close();
});

defineExpose({ play, close });
</script>
