<template>
  <el-dropdown class="px-2" @command="handleCommand">
    <div class="flex items-center">
      <icon size="22px" name="yuyan" color="#333333" />
    </div>

    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item command="zh_CN">中文</el-dropdown-item>
        <el-dropdown-item command="en">English</el-dropdown-item>
        <el-dropdown-item command="ja">日本語</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { useLocale } from "@/locales/useLocale";

import type { LocaleType } from "@/locales/lang";

const { changeLocale } = useLocale();

const isReload = true // 是否刷新页面

const handleCommand = async (command: LocaleType) => {
  await changeLocale(command);
  isReload && location.reload();
};

</script>
