<template>
  <div class="side" :style="sideStyle">
    <div v-if="settingStore.showLogo" class="logo-wrap">
      <img class="big-logo" v-if="!isCollapsed" :src="locale == 'zh_CN' ? logoCN : logoEN" alt="" srcset="" />
      <img v-else class="img-mini" src="../../../../assets/images/logo_mini.png" alt="" srcset="" />
    </div>
    <side-menu
      :routes="routes"
      :isCollapsed="isCollapsed"
      :width="settingStore.sideWidth"
      :unique-opened="settingStore.isUniqueOpened"
      :config="menuProp"
      :theme="sideTheme"
      @select="handleSelect"
    />
  </div>
</template>

<script setup lang="ts">
import useAppStore from "@/stores/modules/app";
import useSettingStore from "@/stores/modules/setting";
import useUserStore from "@/stores/modules/user";
import SideMenu from "./menu.vue";
import { useI18n } from "vue-i18n";
import logoCN from "@/assets/images/logo_dark.png";
import logoEN from "@/assets/images/logo_english_black.webp"; 

const { t, locale } = useI18n();
const appStore = useAppStore();
const isCollapsed = computed(() => {
  if (appStore.isMobile) {
    return false;
  } else {
    return appStore.isCollapsed;
  }
});

const settingStore = useSettingStore();
const sideTheme = computed(() => settingStore.sideTheme);
const userStore = useUserStore();

const routes = computed(() => userStore.routes);

const sideStyle = computed(() => {
  return sideTheme.value == "dark"
    ? {
        "--side-dark-color": settingStore.sideDarkColor,
      }
    : "";
});
const menuProp = computed(() => {
  return {
    backgroundColor: sideTheme.value == "dark" ? settingStore.sideDarkColor : "",
    textColor: sideTheme.value == "dark" ? "var(--el-color-white)" : "",
    activeTextColor: sideTheme.value == "dark" ? "var(--el-color-white)" : "",
  };
});
const handleSelect = () => {
  if (appStore.isMobile) {
    appStore.toggleCollapsed(true);
  }
};
</script>

<style lang="scss" scoped>
.side {
  position: relative;
  z-index: 999;
  @apply border-r border-br-light h-full flex flex-col;
  background-color: var(--side-dark-color, var(--el-bg-color));
  .logo-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    .big-logo {
      width: 150px;
      height: 48px;
      margin: 12px 0;
      object-fit: contain;
    }
    .img-mini {
      width: 40px;
      height: 40px;
      margin: 16px 0;
      object-fit: contain;
    }
  }
}
</style>
