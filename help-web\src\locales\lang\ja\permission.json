{"人员账号": "ユーザーアカウント", "人员名称": "ユーザー名", "人员角色": "ユーザーロール", "查询": "検索", "重置": "リセット", "账号": "アカウント", "名称": "名前", "角色": "役割", "邮箱": "メール", "关联车辆": "関連車両", "状态": "ステータス", "密码": "パスワード", "确认密码": "パスワード確認", "请输入账号": "アカウントを入力してください", "请输入名称": "名前を入力してください", "请选择车辆": "車両を選択してください", "创建时间": "作成時間", "最近登录时间": "最近のログイン時間", "编辑人员": "ユーザー編集", "新增角色": "新しい役割を追加", "备注": "備考", "排序": "ソート", "正常": "有効", "停用": "無効", "权限": "権限", "权限设置": "権限設定", "展开折叠": "展開/折り畳み", "全选不全选": "全選択/全選択解除", "父子联动": "親子連動", "新增菜单": "新しいメニューを追加", "下载当前菜单": "現在のメニューをダウンロード", "上传菜单": "メニューをアップロード", "图标": "アイコン", "权限标识": "権限識別子", "更新时间": "更新時間", "菜单类型": "メニュータイプ", "目录": "ディレクトリ", "菜单": "メニュー", "按钮": "ボタン", "父级菜单": "親メニュー", "请选择父级菜单": "親メニューを選択してください", "菜单名称": "メニュー名", "请输入菜单名称": "メニュー名を入力してください", "菜单图标": "メニューアイコン", "无": "なし", "搜索图标": "アイコンを検索", "路由路径": "ルートパス", "请输入路由路径": "ルートパスを入力してください", "访问的路由地址": "アクセスするルートアドレス、例：`admin`。外部アドレスの場合は`http(s)://`で始めます。", "是否显示": "表示するか", "显示": "表示", "隐藏": "非表示", "选择隐藏则路由将不会出现在侧边栏": "非表示を選択すると、ルートはサイドバーに表示されませんが、アクセスは可能です", "菜单状态": "メニューステータス", "选择停用则路由将不会出现在侧边栏": "無効を選択すると、ルートはサイドバーに表示されず、アクセスもできません", "菜单排序": "メニュー順序", "数值越大越排前": "数値が大きいほど前に表示されます", "组件路径": "コンポーネントパス", "请输入组件路径": "コンポーネントパスを入力してください", "访问的组件路径": "アクセスするコンポーネントパス、例：`permission/admin/index`。デフォルトは`views`ディレクトリ内です", "选中菜单": "選択メニュー", "请输入选中菜单": "選択メニューを入力してください", "访问详情页面菜单高亮显示": "詳細ページや編集ページにアクセスする際、メニューをハイライト表示します。例：`/consumer/lists`", "权限字符": "権限文字", "请输入权限字符": "権限文字を入力してください", "将作为验权使用": "サーバー側APIの権限検証に使用されます。例：`system:admin:list`。慎重に変更してください", "路由参数": "ルートパラメータ", "请输入路由参数": "ルートパラメータを入力してください", "访问路由的默认传递参数": "ルートアクセスのデフォルトパラメータ、例：`{id: 1, name: admin}`または`id=1&name=admin`", "是否缓存": "キャッシュするか", "缓存": "キャッシュ", "不缓存": "キャッシュしない", "选择缓存则会被缓存": "キャッシュを選択すると`keep-alive`にキャッシュされます"}