import { defineStore } from "pinia";

type Ping = {
  ping: number;
  jitter: number;
};
interface DataItem {
  name: string;
  value: [string, number];
}

interface PingData {
  ping: DataItem[] | Array<Object>;
  jitter: DataItem[] | Array<Object>;
}

interface NetworkState {
  webPing: PingData;
  androidPing: PingData;
  carPing: PingData;
}

const useNetworkStore = defineStore({
  id: "network",
  state: (): NetworkState => ({
    webPing: {
      ping: [],
      jitter: [],
    },
    androidPing: {
      ping: [],
      jitter: [],
    },
    carPing: {
      ping: [],
      jitter: [],
    },
  }),
  actions: {},
});

export default useNetworkStore;
