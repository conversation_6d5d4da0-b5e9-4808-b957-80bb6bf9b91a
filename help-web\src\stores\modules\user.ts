import { defineStore } from "pinia";
import cache from "@/utils/cache";
import type { RouteRecordRaw } from "vue-router";
import { getUserInfo, login, logout, getMenu } from "@/api/user";
import router, { filterAsyncRoutes } from "@/router";
import { TOKEN_KEY } from "@/enums/cacheEnums";
import { PageEnum } from "@/enums/pageEnum";
import { clearAuthInfo, getToken } from "@/utils/auth";

import ja_router from "@/locales/lang/ja/router.json";
import en_router from "@/locales/lang/en/router.json";
import zh_router from "@/locales/lang/zh-CN/router.json";

export interface UserState {
  token: string;
  userInfo: Record<string, any>;
  routes: RouteRecordRaw[];
  menu: any[];
  perms: string[];
}

const useUserStore = defineStore({
  id: "user",
  state: (): UserState => ({
    token: getToken() || "",
    // 用户信息
    userInfo: {},
    // 路由
    routes: [],
    menu: [],
    // 权限
    perms: [],
  }),
  getters: {},
  actions: {
    resetState() {
      this.token = "";
      this.userInfo = {};
      this.perms = [];
    },
    login(playload: any) {
      const { account, password } = playload;
      return new Promise((resolve, reject) => {
        login({
          username: account,
          password: password,
        })
          .then((data) => {
            const Authorization = `Bearer ${data.token}`;
            this.token = Authorization;
            cache.set(TOKEN_KEY, Authorization);
            resolve(data);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    logout() {
      return new Promise((resolve, reject) => {
        logout()
          .then(async (data) => {
            this.token = "";
            await router.push(PageEnum.LOGIN);
            clearAuthInfo();
            resolve(data);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    getUserInfo() {
      return new Promise((resolve, reject) => {
        getUserInfo()
          .then((data) => {
            this.userInfo = data.user;
            this.perms = data.permissions;
            resolve(data);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    getMenu() {
      return new Promise((resolve, reject) => {
        getMenu()
          .then(async ({ lists: data }) => {
            const curLocale = JSON.parse(localStorage.getItem("locale") as string).localInfo.locale || "zh_CN";
            const routeFile: any = {
              "zh_CN": zh_router,
              ja: ja_router,
              en: en_router,
            };
            const langData = routeFile[curLocale];
            const routerLangCheck = (routerData: any) => {
              return routerData.map((route: any) => {
                if (route.children) {
                  route.children = routerLangCheck(route.children);
                }
                if (langData[route.menuName]) {
                  route.menuName = langData[route.menuName];
                }
                return route;
              });
            };

            data = await routerLangCheck(data);
            this.menu = data;
            this.routes = filterAsyncRoutes(data);
            resolve(data);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
  },
});

export default useUserStore;
