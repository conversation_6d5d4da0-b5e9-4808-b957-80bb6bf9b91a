// 节点类型
export enum ROS_NODE_ENUM {
  "stream" = "video_composite",
  "video" = "video_node",
}

// 1.软件 2.硬件
export const ANSWER_TYPE: any = {
  1: "软件",
  2: "硬件",
  3: "其他",
};
export const ANSWER_TYPE_JA: any = {
  1: "ソフト",
  2: "ハード",
  3: "その他",
};
// 1.操作台 2.车端 3.操作台和车端都有
export const DEVICE_TYPE: any = {
  1: "操作台",
  2: "机械端",
  3: "操作台和机械端",
  4: "其他",
};
export const DEVICE_TYPE_JA: any = {
  1: "操作台",
  2: "重機側",
  3: "操作台＆重機側",
  4: "その他",
};



import { i18n } from "@/locales/setupI18n";
// 车辆类型
export const VEHICLE_TYPE_LIST: any = () => {
  return {
    "1": i18n.global.t("vehicle.挖掘机"),
    "2": i18n.global.t("vehicle.电铲"),
    "4": i18n.global.t("vehicle.装载机"),
    "5": i18n.global.t("vehicle.钻机"),
  };
};

// 相机类型
export const CAMERA_TYPE_LIST = [
  { value: "MVG2CB-NONE", label: "未配置" },
  { value: "MVG2CB-001A", label: "BUX-001A" },
  { value: "MVG2CB-001D", label: "BUX-001D" },
  { value: "MVG2CB-001K", label: "BUX-001K" },
  { value: "MVG2CB-001B", label: "BUX-001B" },
  { value: "MVG2CB-001C", label: "BUX-001C" },
  { value: "MVG2CB-001E", label: "BUX-001E" },
  { value: "MVG2CB-001F", label: "BUX-001F" },
  { value: "MVG2CB-001G", label: "BUX-001G" },
  { value: "MVG2CB-001H", label: "BUX-001H" },
  { value: "MVG2CB-001I", label: "BUX-001I" },
  { value: "MVG2CB-001J", label: "BUX-001J" },
  { value: "MVG2CB-001L", label: "BUX-001L" },
  { value: "MVG2CB-001M", label: "BUX-001M" },
  { value: "MVG2CB-002A", label: "BUX-002A" },
  { value: "MVG2CB-003A", label: "BUX-003A" },
  { value: "MVG2CB-004A", label: "BUX-004A" },
  { value: "MVG2CB-004B", label: "BUX-004B" },
  { value: "MVG2CB-004C", label: "BUX-004C" },
  { value: "MVG2CB-005A", label: "BUX-005A" },
  { value: "MVG2CB-005B", label: "BUX-005B" },
  { value: "MVG2CB-005C", label: "BUX-005C" },
  { value: "MVG2CB-006A", label: "BUX-006A" },
  { value: "MVG2CB-006B", label: "BUX-006B" },
  { value: "MVG2CB-006C", label: "BUX-006C" },
  { value: "MVG2CB-006D", label: "BUX-006D" },
  { value: "MVG2CB-006F", label: "BUX-006F" },
  { value: "MVG2CB-006H", label: "BUX-006H" },
  { value: "MVG2CB-006G", label: "BUX-006G" },
  { value: "MVG2CB-006I", label: "BUX-006I" },
  { value: "MVG2CB-007A", label: "BUX-007A" },
  { value: "MVG2CB-0080", label: "BUX-0080" },
];

// 表单字段类型
export const FORM_TYPE_LIST = [
  { value: "input", label: "文本输入框" },
  { value: "inputNumber", label: "数字输入框" },
  { value: "radio", label: "单选框" },
  { value: "select", label: "下拉框" },
  { value: "switch", label: "开关" },
  { value: "slider", label: "滑块" },
  { value: "title", label: "标题" },
  { value: "newline", label: "换行" },
];

export const ROS_NODE_DATA = [
  {
    node_name: "real_time_composite0",
    package_name: "builderx_video",
    node_type: "video_composite",
    program_type: "ros",
    params: {
      width: 1920,
      height: 1080,
      name: "comp_video0",
      camera_list: {
        camera_0: {
          detail: "main",
          device: "main_video",
          primary: 1,
          width: 1920,
          height: 1080,
          left: 0,
          top: 0,
        },
      },
    },
  },
  {
    node_name: "real_time_stream0",
    package_name: "builderx_video",
    node_type: "video_node",
    program_type: "ros",
    params: {
      rtsp_uri: "",
      fps: 30,
      width: 1920,
      height: 1080,
      device: "comp_video0",
      detail: "主视角",
    },
  },
];

export const ANDROID_RESTART_PARAMS = [
  "easyPlayUrl1",
  "easyPlayUrl6",
  "mqttClientID",
  "mqttHost",
  "mqttName",
  "mqttPassword",
  "mqttToAndroid",
  "mqttToExcavator",
  "mqttToAndroidPowerON",
  "androidToMqttPowerON",
  "useArduino",
  "niRenDTO",
  "excavatorType",
  "needPowerOn",
  "powerQuery",
  "doubleDipAngle",
  "doubleFlashTurnLamp",
  "useUSBHandler",
  "useWebRtc",
  "mqttToAndroidTripartite",
  "mqttToAndroidTripartiteEx001",
  "mqttKeepLive",
  "mqttToTripartite",
  "simulatePedal",
  "haveHaiKang",
  "videoParamsSetting",
  "allowPowerOff",
  "allowPowerOn",
  "showPowerOffSwitch",
  "usesTheLeftHandle",
  "videoType",
];