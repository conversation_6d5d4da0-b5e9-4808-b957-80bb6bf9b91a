/**
 * 文件类型判断工具函数
 * 与后端素材管理模块的文件类型判断保持一致
 */

/**
 * 根据文件扩展名判断文件类型
 * @param filename 文件名或扩展名
 * @returns 文件类型: 'image' | 'video' | 'audio' | 'other'
 */
export function getFileTypeByExtension(filename: string): string {
  if (!filename) return 'other';
  
  // 提取文件扩展名
  const ext = filename.split('.').pop()?.toLowerCase() || '';
  
  // 图片类型
  const imageExtensions = new Set(['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico', 'tiff', 'tif']);
  // 视频类型
  const videoExtensions = new Set(['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp', 'rm', 'rmvb']);
  // 音频类型
  const audioExtensions = new Set(['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a']);
  
  if (imageExtensions.has(ext)) {
    return 'image';
  } else if (videoExtensions.has(ext)) {
    return 'video';
  } else if (audioExtensions.has(ext)) {
    return 'audio';
  } else {
    return 'other';
  }
}

/**
 * 判断是否为图片文件
 * @param filename 文件名
 * @returns boolean
 */
export function isImageFile(filename: string): boolean {
  return getFileTypeByExtension(filename) === 'image';
}

/**
 * 判断是否为视频文件
 * @param filename 文件名
 * @returns boolean
 */
export function isVideoFile(filename: string): boolean {
  return getFileTypeByExtension(filename) === 'video';
}

/**
 * 判断是否为音频文件
 * @param filename 文件名
 * @returns boolean
 */
export function isAudioFile(filename: string): boolean {
  return getFileTypeByExtension(filename) === 'audio';
}

/**
 * 获取文件类型对应的图标
 * @param filename 文件名
 * @returns 图标名称
 */
export function getFileTypeIcon(filename: string): string {
  const fileType = getFileTypeByExtension(filename);
  
  switch (fileType) {
    case 'image':
      return 'el-icon-Picture';
    case 'video':
      return 'el-icon-VideoPlay';
    case 'audio':
      return 'el-icon-Headphone';
    default:
      return 'el-icon-Document';
  }
}

/**
 * 获取文件类型对应的颜色
 * @param filename 文件名
 * @returns 颜色值
 */
export function getFileTypeColor(filename: string): string {
  const fileType = getFileTypeByExtension(filename);
  
  switch (fileType) {
    case 'image':
      return '#67C23A'; // 绿色
    case 'video':
      return '#409EFF'; // 蓝色
    case 'audio':
      return '#E6A23C'; // 橙色
    default:
      return '#909399'; // 灰色
  }
}
