<template>
  <div ref="chartRef" class="w-full h-full mx-0 my-auto"></div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import "echarts-liquidfill";
import { timeFormat } from "@/utils/util";
import useWebsocketStore from "@/stores/modules/websocket";

const webStore = useWebsocketStore();
const chartRef = ref();
const chart = ref();

onMounted(() => {
  setTimeout(() => {
    chart.value = echarts.init(chartRef.value);
    chart.value.setOption(options);
  }, 100);
  let count = 0;
  setInterval(() => {
    if (!chart.value) return;
    if (Object.keys(webStore.controlChartData).length > 0) {
      count++;
      if (count > 5 * 60) {
        chart.value.setOption({
          xAxis: {
            min: timeFormat(Date.now() - 5 * 60000, "yyyy-mm-dd hh:MM:ss"),
            max: timeFormat(Date.now(), "yyyy-mm-dd hh:MM:ss"),
          },
          series: [{ data: webStore.controlChartData }],
        });
      } else {
        chart.value.setOption({
          series: [{ data: webStore.controlChartData }],
        });
      }
    }
  }, 1000);
});

const options = {
  xAxis: {
    type: "time",
    min: timeFormat(Date.now(), "yyyy-mm-dd hh:MM:ss"),
    max: timeFormat(Date.now() + 5 * 60000, "yyyy-mm-dd hh:MM:ss"),
    boundaryGap: false, // 不留白，从原点开始
    splitLine: {
      show: true,
      lineStyle: {
        color: "rgba(31,99,163,.2)",
      },
    },
    axisLine: {
      // show:false,
      lineStyle: {
        color: "rgba(31,99,163,.1)",
      },
    },
    axisLabel: {
      color: "#7EB7FD",
      fontWeight: "500",
    },
  },
  yAxis: {
    type: "value",
    splitLine: {
      show: true,
      lineStyle: {
        color: "rgba(31,99,163,.2)",
      },
    },
    axisLine: {
      lineStyle: {
        color: "rgba(31,99,163,.1)",
      },
    },
    axisLabel: {
      color: "#7EB7FD",
      fontWeight: "500",
    },
  },
  tooltip: {
    trigger: "axis",
    backgroundColor: "rgba(0,0,0,.6)",
    borderColor: "rgba(147, 235, 248, .8)",
    textStyle: {
      color: "#FFF",
    },
  },
  grid: {
    //布局
    show: true,
    left: "30px",
    right: "40px",
    bottom: "20px",
    top: "40px",
    containLabel: true,
    borderColor: "#1F63A3",
  },
  series: [
    {
      data: [
        {
          name: "操作强度",
          value: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
        },
      ],
      type: "line",
      smooth: true,
      symbol: "none", //去除点
      name: "",
      color: "rgba(9,202,243,.7)",
      areaStyle: {
        //右，下，左，上
        color: new echarts.graphic.LinearGradient(
          0,
          0,
          0,
          1,
          [
            {
              offset: 0,
              color: "rgba(9,202,243,.7)",
            },
            {
              offset: 1,
              color: "rgba(9,202,243,.0)",
            },
          ],
          false
        ),
      },
      // markPoint: {
      //   data: [
      //     {
      //       name: "最大值",
      //       type: "max",
      //       valueDim: "y",
      //       symbol: "rect",
      //       symbolSize: [60, 26],
      //       symbolOffset: [0, -20],
      //       itemStyle: {
      //         color: "rgba(0,0,0,0)",
      //       },
      //       label: {
      //         color: "#FC9010",
      //         backgroundColor: "rgba(252,144,16,0.1)",
      //         borderRadius: 6,
      //         padding: [7, 14],
      //         borderWidth: 0.5,
      //         borderColor: "rgba(252,144,16,.5)",
      //         formatter: "报警1：{c}",
      //       },
      //     },
      //     {
      //       name: "最大值",
      //       type: "max",
      //       valueDim: "y",
      //       symbol: "circle",
      //       symbolSize: 6,
      //       itemStyle: {
      //         color: "#FC9010",
      //         shadowColor: "#FC9010",
      //         shadowBlur: 8,
      //       },
      //       label: {
      //         formatter: "",
      //       },
      //     },
      //   ],
      // },
    },
  ],
};

onUnmounted(() => {
  if (chart.value) {
    chart.value.dispose();
    chart.value = null;
  }
});
</script>
<style scoped lang="scss"></style>
