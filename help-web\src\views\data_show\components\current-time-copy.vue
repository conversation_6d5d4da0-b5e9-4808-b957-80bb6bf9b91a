<template>
  <div class="current-time">
    <div class="logoline3"></div>
    <div class="date-timer">{{ pageData.time }}</div>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";

const pageData = reactive({
  time: "",
});

let timer: any;

onMounted(() => {
  timer = setInterval(() => {
    pageData.time = dayjs().format("mm分ss秒");
  }, 1000);
});

onUnmounted(() => {
  if (timer) clearInterval(timer);
});
</script>

<style scoped lang="scss">
@font-face {
  font-family: "electronicFont";
  src: url("../css/font/DS-DIGIT.TTF");
}
.current-time {
  height: 100%;
  width: 100%;
  color: #fff;
  font-family: "electronicFont", "microsoft yahei", simhei;
  moz-user-select: -moz-none;
  moz-user-select: none;
  -o-user-select: none;
  -khtml-user-select: none;
  user-select: none;
  -ms-user-select: none;
  user-select: none;
  overflow-x: hidden;
  animation: fadeIn 1s 0.2s ease both;
  -moz-animation: fadeIn 1s 0.2s ease both;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.logoline3 {
  height: 86%;
  width: auto; /* 高度自动 */
  aspect-ratio: 1 / 1; /* 宽高比为 1:1，形成正方形 */
  background: url("../images/logoline3.png") 50% 50% no-repeat;
  background-size: 100% 100%;
  animation: forRotate 5s infinite linear;
}

.date-timer {
  position: absolute;
  font-size: 26px;
  letter-spacing: 2px;
  animation: fadeInUp 2.2s 0.2s ease both;
}
/*淡入*/
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes forRotate {
  from {
    transform: rotate(-360deg);
    opacity: 0.9;
  }
  to {
    transform: rotate(0);
    opacity: 1;
  }
}
</style>
