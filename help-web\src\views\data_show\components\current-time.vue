<template>
  <div class="current-time">
    <h2>
      <strong>重点关注</strong><sub>Focus on the indicators</sub><b class="logoline"></b><b class="logoline1"></b>
      <b class="logoline2"></b>
      <b class="logoline3"></b>
    </h2>
    <div class="date-timer">
      <p>
        <strong id="H">{{ timeData.h }}</strong>
        <strong>:</strong>
        <strong id="M">{{ timeData.m }}</strong>
      </p>
      <em id="D">{{ timeData.d }}</em>
      <ul>
        <li id="Y">{{ timeData.y }}</li>
        <li id="MH">{{ timeData.month }}</li>
        <li id="TD">{{ timeData.td }}</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
const timeData = reactive({
  h: "",
  m: "",
  s: "",
  d: "",
  y: "",
  month: "",
  td: "",
});

const getTime = () => {
  let today = new Date();
  let weekday = new Array(7);
  weekday[0] = "星期一";
  weekday[1] = "星期二";
  weekday[2] = "星期三";
  weekday[3] = "星期四";
  weekday[4] = "星期五";
  weekday[5] = "星期六";
  weekday[6] = "星期日";
  let y = today.getFullYear() + "年";
  let month = today.getMonth() + 1 + "月";
  let td = today.getDate() + "日";
  let d = weekday[today.getDay() - 1];
  let h = today.getHours() as any;
  let m = today.getMinutes() as any;
  let s = today.getSeconds() as any;
  if (h < 10) {
    h = "0" + h;
  }
  if (m < 10) {
    m = "0" + m;
  }
  timeData.h = h;
  timeData.m = m;
  timeData.s = s;
  timeData.d = d;
  timeData.y = y;
  timeData.month = month;
  timeData.td = td;
};

let interval: NodeJS.Timeout | null = null;
interval = setInterval(getTime, 1000);

onMounted(() => {
  getTime();
});

onUnmounted(() => {
  if (interval) clearInterval(interval);
});
</script>

<style scoped lang="scss">
@font-face {
  font-family: "electronicFont";
  src: url("../css/font/DS-DIGIT.TTF");
}

.current-time {
  height: 190px;
  width: 246px;
  position: relative;
  background-color: rgb(240, 157, 49);
  color: #fff;
  font-family: "microsoft yahei", simhei;
  moz-user-select: -moz-none;
  moz-user-select: none;
  -o-user-select: none;
  -khtml-user-select: none;
  user-select: none;
  -ms-user-select: none;
  user-select: none;
  overflow-x: hidden;
  animation: fadeIn 1s 0.2s ease both;
  -moz-animation: fadeIn 1s 0.2s ease both;
}

h2 {
  font-size: 24px;
  font-weight: normal;
  position: absolute;
  left: 34px;
  top: 81px;
  padding-bottom: 20px;
}
h2 sub {
  position: absolute;
  left: 0;
  bottom: 0;
  display: block;
  width: 92px;
  height: 8px;
  background: url("../images/logofont.png") 50% 50% no-repeat;
  text-indent: -500px;
}
.logoline {
  position: absolute;
  left: 0;
  top: 35px;
  width: 206px;
  height: 2px;
  background: url("../images/logoline.png") 50% 50% no-repeat;
  display: block;
}
.logoline1 {
  position: absolute;
  z-index: 3;
  left: -30px;
  top: -34px;
  width: 41px;
  height: 29px;
  background: url("../images/logoline1.png") 50% 50% no-repeat;
  display: block;
}
.logoline2 {
  position: absolute;
  z-index: 3;
  left: 55px;
  top: 58px;
  width: 152px;
  height: 26px;
  background: url("../images/logoline2.png") 50% 50% no-repeat;
  display: block;
}
.logoline3 {
  position: absolute;
  z-index: 0;
  left: -10px;
  top: -41px;
  width: 121px;
  height: 121px;
  background: url("../images/logoline3.png") 50% 50% no-repeat;
  display: block;
  animation: forRotate 5s infinite linear;
}
h2 strong {
  font-weight: normal;
  animation-iteration-count: infinite;
  animation-name: bluePulse;
  animation-duration: 2s;
}

.date-timer {
  text-align: center;
  position: absolute;
  left: 146px;
  top: 72px;
}
.date-timer strong {
  display: inline-block;
  font-family: "electronicFont";
  font-size: 36px;
  line-height: 50px;
  padding-bottom: 0px;
}
.date-timer em {
  display: block;
  font-style: normal;
}
.date-timer ul {
  font-family: "electronicFont";
  font-size: 12px;
  margin-top: 0px;
}
.date-timer ul li {
  display: inline-block;
}
.date-timer {
  animation: fadeInUp 2.2s 0.2s ease both;
}
/*淡入*/
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes forRotate {
  from {
    transform: rotate(-360deg);
    opacity: 0.9;
  }
  to {
    transform: rotate(0);
    opacity: 1;
  }
}
</style>
