<template>
  <div class="handle-wrap flex justify-between">
    <div class="handle-item h-20 w-20 bg-primary-light-3 relative">
      <div
        class="handle-ball absolute h-4 w-4 rounded-[50%] bg-white"
        :style="ballStyle(leftJoystickX, leftJoystickY)"
      ></div>
    </div>
    <div class="foot-item h-20 w-4 bg-primary-light-3 relative rounded-[4px] flex justify-center">
      <div class="absolute h-4 w-4 rounded-[50%] bg-white" :style="footStyle(leftPedal)"></div>
    </div>
    <div class="foot-item h-20 w-4 bg-primary-light-3 relative rounded-[4px] flex justify-center">
      <div class="absolute h-4 w-4 rounded-[50%] bg-white" :style="footStyle(rightPedal)"></div>
    </div>
    <div class="handle-item h-20 w-20 bg-primary-light-3 relative">
      <div
        class="handle-ball absolute h-4 w-4 rounded-[50%] bg-white"
        :style="ballStyle(rightJoystickX, rightJoystickY)"
      ></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps({
  leftJoystickX: {
    type: Number,
    default: 0.0,
  },
  leftJoystickY: {
    type: Number,
    default: 0.0,
  },
  leftPedal: {
    type: Number,
    default: 0.0,
  },
  rightJoystickX: {
    type: Number,
    default: 0.0,
  },
  rightJoystickY: {
    type: Number,
    default: 0.0,
  },
  rightPedal: {
    type: Number,
    default: 0.0,
  },
});

const { leftJoystickX, leftJoystickY, leftPedal, rightJoystickX, rightJoystickY, rightPedal } = toRefs(props);

const ballStyle = computed(() => (handleX: any, handleY: any) => {
  // 将值归一化到[-1, 1]
  const normalize = (val: any) => val / 100;
  const xNorm = normalize(handleX);
  const yNorm = normalize(handleY);

  // 计算极坐标半径
  const r = Math.min(1, Math.sqrt(xNorm * xNorm + yNorm * yNorm));

  // 计算角度
  const theta = Math.atan2(yNorm, xNorm);

  // 将极坐标转换为圆形内的直角坐标
  const x = r * Math.cos(theta) * 40; // 40是圆形半径
  const y = r * Math.sin(theta) * 40; // 40是圆形半径

  return {
    transform: `translate(${x + 40 - 8}px, ${40 - y - 8}px)`, // 加上圆心偏移和小球半径调整
  };
});

const footStyle = computed(() => (footValue: any) => {
  const normalize = (val: any) => ((val + 100) / 200) * 80; // 将值从[-100, 100]映射到[0, 80]，80是长条的高度
  const y = normalize(footValue);
  return {
    transform: `translateY(${80 - y - 8}px)`, // 80是长条的高度，8是小球的半径
  };
});

onMounted(() => {});
</script>

<style lang="scss" scoped>
.handle-item {
  z-index: 9999;
  border-radius: 50%; /* 将正方形变成圆形 */
  overflow: hidden; /* 确保小球不超出圆形边界 */
}

.handle-ball {
  background-color: white;
  transition: transform 0.5s ease; /* 添加动画效果 */
}

.foot-item .bg-white {
  transition: transform 0.5s ease; /* 添加动画效果 */
}
</style>
