<template>
  <div ref="chartRef" class="w-full h-full mx-0 my-auto"></div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import useWebsocketStore from "@/stores/modules/websocket";

const webStore = useWebsocketStore();
const chartRef = ref();
const chart = ref();

let demoData = [
  { name: "油压", value: 220, unit: "kPa", pos: ["25%", "28%"], range: [0, 800] },
  { name: "电压", value: 32, unit: "V", pos: ["75%", "28%"], range: [20, 30] },
  { name: "油耗", value: 2, unit: "L/h", pos: ["25%", "75%"], range: [0, 30] },
  { name: "燃油水平", value: 68, unit: "L/h", pos: ["75%", "75%"], range: [0, 100] },
];

onMounted(() => {
  setTimeout(() => {
    chart.value = echarts.init(chartRef.value);
    chart.value.setOption(getOptions());
  }, 100);

  setInterval(() => {
    if (!chart.value) return;

    for (let i = 0; i < demoData.length; i++) {
      const element = demoData[i];
      if (element.name === "油压") {
        element.value = webStore.vehicleInfo.oil_pressure;
      }
      if (element.name === "电压") {
        element.value = webStore.vehicleInfo.battery_voltage.toFixed(1);
      }
      if (element.name === "油耗") {
        element.value = webStore.vehicleInfo.ambient_temperature.toFixed(1);
      }
      if (element.name === "燃油水平") {
        element.value = webStore.vehicleInfo.fuel_level.toFixed(1);
      }
    }
    chart.value.setOption(getOptions());
  }, 1000);
});

let highlight = "#06b6d4";

const getOptions = () => {
  let result: any = [];
  demoData.forEach(function (item: any) {
    result.push(
      // 外围刻度
      {
        type: "gauge",
        center: item.pos,
        radius: "30%", // 1行2个，增加间距
        splitNumber: item.splitNum || 10,
        min: item.range[0],
        max: item.range[1],
        startAngle: 225,
        endAngle: -45,
        axisLine: {
          show: true,
          lineStyle: {
            width: 2,
            shadowBlur: 0,
            color: [[1, highlight]],
          },
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: highlight,
            width: 1,
          },
          length: -5,
          splitNumber: 10,
        },
        splitLine: {
          show: true,
          length: -14,
          lineStyle: {
            color: highlight,
          },
        },
        axisLabel: {
          distance: -20,
          textStyle: {
            color: highlight,
            fontSize: "12",
            fontWeight: "bold",
          },
        },
        pointer: {
          show: 0,
        },
        detail: {
          show: 0,
        },
      },

      // 内侧指针、数值显示
      {
        name: item.name,
        type: "gauge",
        center: item.pos,
        radius: "29%",
        startAngle: 225,
        endAngle: -45,
        min: item.range[0],
        max: item.range[1],
        axisLine: {
          show: true,
          lineStyle: {
            width: 16,
            color: [[1, "rgba(255,255,255,.1)"]],
          },
        },
        axisTick: {
          show: 0,
        },
        splitLine: {
          show: 0,
        },
        axisLabel: {
          show: 0,
        },
        pointer: {
          show: true,
          length: "105%",
        },
        detail: {
          show: true,
          offsetCenter: [0, "90%"],
          textStyle: {
            fontSize: 14,
            color: "#fff",
            lineHeight: 16,
          },
          formatter: ["{value} " + (item.unit || ""), "{name|" + item.name + "}"].join("\n"),
          rich: {
            name: {
              fontSize: 14,
              lineHeight: 30,
              color: "#ddd",
            },
          },
        },
        itemStyle: {
          normal: {
            color: highlight,
          },
        },
        data: [
          {
            value: item.value,
          },
        ],
      }
    );
  });
  return {
    series: result,
  };
};

onUnmounted(() => {
  if (chart.value) {
    chart.value.dispose();
    chart.value = null;
  }
});
</script>
<style scoped lang="scss"></style>
