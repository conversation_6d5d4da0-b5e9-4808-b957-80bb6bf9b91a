<template>
  <div style="width: 180px">
    <VueDataUi component="VueUiThermometer" :dataset="dataset" :config="config" />
  </div>
</template>

<script setup lang="ts">
import { VueDataUi } from "vue-data-ui";
import "vue-data-ui/style.css";
import type { VueUiThermometerDataset, VueUiThermometerConfig } from "vue-data-ui";

const dataset: VueUiThermometerDataset = {
  value: 37,
  from: -100,
  to: 100,
  steps: 20,
  colors: {
    from: "#dc3912",
    to: "#3366cc",
  },
};

const config: VueUiThermometerConfig = {
  style: {
    fontFamily: "inherit",
    chart: {
      backgroundColor: "#1A1A1A",
      color: "#CCCCCC",
      height: 160,
      thermometer: { width: 28 },
      padding: { top: 12, bottom: 12, left: 34, right: 34 },
      graduations: {
        show: true,
        sides: "both",
        height: 2,
        stroke: "#e1e5e8",
        strokeWidth: 1,
        showIntermediate: true,
        gradient: { show: true, intensity: 40 },
      },
      animation: { use: true, speedMs: 1000 },
      label: { fontSize: 14, rounding: 1, bold: true, prefix: "", suffix: "" },
    },
    title: {
      text: "Title",
      color: "#FAFAFA",
      fontSize: 16,
      bold: true,
      textAlign: "center",
      paddingLeft: 0,
      paddingRight: 0,
      subtitle: { color: "#A1A1A1", text: "Subtitle", fontSize: 16, bold: false },
    },
  },
  userOptions: {
    show: false,
    buttons: { pdf: true, img: true, fullscreen: true },
    buttonTitles: {
      open: "Open options",
      close: "Close options",
      pdf: "Download PDF",
      img: "Download PNG",
      fullscreen: "Toggle fullscreen",
    },
  },
};
</script>
<style scoped lang="scss"></style>
