<template>
  <div ref="chartRef" class="w-full h-full mx-0 my-auto"></div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import "echarts-liquidfill";
import useWebsocketStore from "@/stores/modules/websocket";

const webStore = useWebsocketStore();
const chartRef = ref();
const chart = ref();

onMounted(() => {
  setTimeout(() => {
    chart.value = echarts.init(chartRef.value);
    chart.value.setOption(options);
  }, 100);

  setInterval(() => {
    if (!chart.value) return;
    chart.value.setOption({
      series: [
        {
          label: {
            normal: {
              formatter: webStore.vehicleInfo.fuel_level + "%",
            },
          },
        },
      ],
    });
  }, 1000);
});

const options = {
  // 标题配置
  // title: {
  //   text: "操控时长", // 标题文本
  //   bottom: 14, // 标题距离容器底部的距离
  //   left: "center", // 标题居中对齐
  //   textStyle: {
  //     fontSize: 24, // 标题字体大小
  //     color: "#f6f6f6", // 标题字体颜色
  //   },
  // },
  series: [
    {
      type: "liquidFill", // 水波图类型
      radius: "75%", // 水波图的半径
      center: ["50%", "50%"], // 水波图的中心位置
      data: [0.4, 0.4], // 水波图数据，数组长度决定波浪数量
      backgroundStyle: {
        borderWidth: 1, // 背景边框宽度
        color: "#090e1e", // 背景颜色，含透明度
      },
      label: {
        normal: {
          formatter: "0%", // 中间显示的文本
          textStyle: {
            fontSize: 34, // 文本字体大小
            color: "#60a5fa",
          },
        },
      },
      outline: {
        show: false, // 不显示轮廓
      },
    },
    {
      type: "pie", // 饼图类型
      // 外层进度条
      center: ["50%", "50%"], // 饼图中心位置
      radius: ["78%", "70%"], // 饼图的内外半径，形成环形
      hoverAnimation: false, // 禁用悬停动画
      data: [
        {
          name: "",
          value: 100, // 当前部分的值
          labelLine: {
            show: false, // 不显示引导线
          },
          itemStyle: {
            color: "#3b82f6", // 当前部分的颜色
            normal: {
              color: "#3b82f6",
              borderColor: "#3b82f6",
              // borderRadius: '100%'
            },
          },
          emphasis: {
            labelLine: {
              show: false, // 高亮状态下隐藏引导线
            },
            itemStyle: {
              color: "#3b82f6", // 高亮状态下的颜色
            },
          },
        },
      ],
    },
  ],
};

onUnmounted(() => {
  if (chart.value) {
    chart.value.dispose();
    chart.value = null;
  }
});
</script>
<style scoped lang="scss"></style>
