(function (win, lib) {
  var docEl = document.getElementById("DataShow");
  var dpr = 1;
  var tid;
  var flexible = lib.flexible || (lib.flexible = {});

  function refreshRem() {
    var width = docEl.getBoundingClientRect().width;
    console.log(width);
    
    // 最小1366px，最大适配2560px
    if (width / dpr < 720) {
      width = 720 * dpr;
    } else if (width / dpr > 2560) {
      width = 2560 * dpr;
    }
    // 设置成24等份，设计稿时1920px的，这样1rem就是80px
    var rem = width / 24;
    docEl.style.fontSize = rem + "px";
    console.log(docEl.style.fontSize);
    
    flexible.rem = win.rem = rem;
  }

  win.addEventListener(
    "resize",
    function () {
      clearTimeout(tid);
      tid = setTimeout(refreshRem, 300);
    },
    false
  );
  win.addEventListener(
    "pageshow",
    function (e) {
      if (e.persisted) {
        clearTimeout(tid);
        tid = setTimeout(refreshRem, 300);
      }
    },
    false
  );

  refreshRem();

  flexible.dpr = win.dpr = dpr;
  flexible.refreshRem = refreshRem;
})(window, window["lib"] || (window["lib"] = {}));
