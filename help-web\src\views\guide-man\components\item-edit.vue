<template>
  <div class="edit-popup">
    <popup ref="popupRef" :title="popupTitle" :async="true" width="750px" @confirm="handleSubmit" @close="handleClose">
      <el-form ref="formRef" :model="formData" label-width="104px" :rules="formRules">
        <el-form-item label="步骤标题" prop="title">
          <el-input v-model="formData.title" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="指导描述" prop="content">
          <editor v-model="formData.content" :height="400" width="100%" />
        </el-form-item>
      </el-form>
    </popup>
  </div>
</template>

<script lang="ts" setup>
import type { FormInstance } from "element-plus";
import Popup from "@/components/popup/index.vue";
import feedback from "@/utils/feedback";
import { guideAdd, guideEdit } from "@/api/guide-man";

const emit = defineEmits(["save", "edit", "close"]);
const formRef = shallowRef<FormInstance>();
const popupRef = shallowRef<InstanceType<typeof Popup>>();
const mode = ref("add");

const popupTitle = computed(() => ` ${mode.value === "edit" ? "编辑" : "添加"}步骤 `);

const formData: any = reactive({
  id: "",
  title: "",
  content: "",
});

const formRules = reactive({
  title: [
    {
      required: true,
      message: "请输入",
      trigger: ["blur"],
    },
  ],
});

const pageData: any = reactive({});

onMounted(() => {});

const handleSubmit = async () => {
  await formRef.value?.validate();
  popupRef.value?.close();
  if (mode.value == "edit") {
    emit("edit", formData);
  } else {
    emit("save", formData);
  }
};

const open = (type = "add") => {
  mode.value = type;
  popupRef.value?.open();
};

const setFormData = async (row: any) => {
  const data = { id: row.id, ...row };
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key];
    }
  }
};

const handleClose = () => {
  emit("close");
};

defineExpose({
  open,
  setFormData,
});
</script>
