<template>
  <div class="admin">
    <el-card class="mt-4 !border-none" shadow="never">
      <el-page-header class="mb-3" @back="$router.back()">
        <template #content>
          <div class="flex items-center">
            <span class="text-large font-600 mr-3"> {{ pageData.name }} </span>
          </div>
        </template>
        <template #extra>
          <div class="flex items-center">
            <el-button type="primary" @click="handleShowAdd(pageData.content.length)">新增</el-button>
            <el-button type="primary" @click="handleSave"> 保存</el-button>
          </div>
        </template>
      </el-page-header>

      <el-table class="mt-2" :data="pageData.content" size="large" :height="calcTableHeight()" row-key="title">
        <!-- 加展开影响拖动排序 -->
        <!-- <el-table-column type="expand">
          <template #default="props">
            <div v-html="props.row.content"></div>
          </template>
        </el-table-column> -->
        <el-table-column label="步骤标题" prop="title" />
        <el-table-column label="步骤详情" prop="content" />
        <el-table-column align="center" label="操作" fixed="right">
          <template #default="{ row, $index }">
            <el-button link type="primary" @click="handleShowAdd($index)"> 添加 </el-button>
            <el-button link type="primary">编辑</el-button>
            <el-button link type="danger" @click="handleDelete($index)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <edit-popup v-if="showEdit" ref="editRef" @close="showEdit = false" @save="handleAdd" @edit="handleEdit" />
  </div>
</template>

<script lang="ts" setup name="admin">
import feedback from "@/utils/feedback";
import Sortable from "sortablejs";
import EditPopup from "./components/item-edit.vue";
import { guideDetail, guideItemAdd, guideItemEdit, guideItemDelete, guideEdit } from "@/api/guide-man";

const route = useRoute();
const showEdit = ref(false);
const editRef = shallowRef<InstanceType<typeof EditPopup>>();
const pageData: any = reactive({
  id: "",
  name: "",
  content: [],
});

const curAddIndex = ref(0);
const guideId = route.query.id as string;

const calcTableHeight = () => {
  return window.innerHeight - 91 - 16 * 5 - 74 - 20 * 2 - 32 * 1 + 32 * 3;
};

const handleDelete = async (index: any) => {
  await feedback.confirm("确定要删除？");
  await guideItemDelete({ guideId, id: pageData.content[index].id });
  pageData.content.splice(index, 1);
  feedback.msgSuccess("删除成功");
};

const handleShowAdd = async (index: any = 0) => {
  curAddIndex.value = index;
  showEdit.value = true;
  await nextTick();
  editRef.value?.open();
};

const handleAdd = async (row: any) => {
  if (pageData.content.length > 0) {
    pageData.content.splice(curAddIndex.value + 1, 0, row);
  } else {
    pageData.content.push(row);
  }
  showEdit.value = false;
  await guideItemAdd({ guideId, ...row });
  feedback.msgSuccess("添加成功");
};

const handleShowEdit = async (data: any, index: any) => {
  curAddIndex.value = index;
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("edit");
  editRef.value?.setFormData(data);
};

const handleEdit = async (row: any) => {
  pageData.content[curAddIndex.value] = row;
  feedback.msgSuccess("编辑成功");
  showEdit.value = false;
  await guideItemEdit({ guideId, ...row });
};

const handleDetail = async (id: string) => {
  const res = await guideDetail({ id });
  Object.assign(pageData, res);
};

const handleSave = async () => {
  await guideEdit({ guideId, ...pageData });
  feedback.msgSuccess("保存成功");
};

const initDropTable = () => {
  let tbody = document.querySelector(".el-table__body-wrapper tbody");
  Sortable.create(tbody, {
    animation: 150, // ms, number 单位：ms，定义排序动画的时间
    onEnd(e: any) {
      if (e.oldIndex !== e.newIndex) {
        let currRow = pageData.content.splice(e.oldIndex, 1)[0];
        pageData.content.splice(e.newIndex, 0, currRow);
      }
    },
  });
};

onMounted(() => {
  handleDetail(guideId);
  initDropTable();
});
</script>
<style lang="scss" scoped>
.text_hidden {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}
</style>
