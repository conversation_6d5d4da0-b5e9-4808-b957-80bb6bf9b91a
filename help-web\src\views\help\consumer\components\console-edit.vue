<template>
  <div class="edit-popup">
    <popup
      ref="popupRef"
      :title="popupTitle"
      :async="true"
      width="450px"
      @confirm="handleSubmit"
      @close="handleClose"
      :confirmButtonText="$t('stream.确定')"
      :cancelButtonText="$t('stream.取消')"
    >
      <el-form ref="formRef" :model="formData" label-width="104px" :rules="formRules">
        <el-form-item label="操作台番号" prop="console_name">
          <el-input v-model="formData.console_name" placeholder="例：操作台001" clearable />
        </el-form-item>
        <!-- <el-form-item label="操作台类型" prop="console_type">
          <el-select v-model="formData.console_type" placeholder="请选择操作台类型" clearable>
            <el-option
              v-for="item in consoleTypeList"
              :key="item.flag"
              :label="item.name"
              :value="Number(item.flag)"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item label="操作台SN番号" prop="console_sn">
          <el-input v-model="formData.console_sn" placeholder="操作台SN番号を入力してください" clearable />
        </el-form-item>
        <el-form-item label="所在地" prop="console_address">
          <el-input v-model="formData.console_address" placeholder="所在地を入力してください" clearable />
        </el-form-item>
        <el-form-item label="取り付け日" prop="install_date">
          <el-date-picker
            v-model="formData.install_date"
            type="date"
            placeholder="取り付け日を選択してください"
            clearable
          />
        </el-form-item>
        <el-form-item label="引き渡し日" prop="delivery_date">
          <el-date-picker
            v-model="formData.delivery_date"
            type="date"
            placeholder="引き渡し日を選択してください"
            clearable
          />
        </el-form-item>
        <el-form-item label="保証終了日" prop="warranty_end_date">
          <el-date-picker
            v-model="formData.warranty_end_date"
            type="date"
            placeholder="保証終了日を選択してください"
            clearable
          />
        </el-form-item>
      </el-form>
    </popup>
  </div>
</template>

<script lang="ts" setup>
import type { FormInstance } from "element-plus";
import Popup from "@/components/popup/index.vue";
import feedback from "@/utils/feedback";
import useMetadataStore from "@/stores/modules/metadata";
import { projectAddConsole, projectEditConsole } from "@/api/project-man";

const metadataStore = useMetadataStore();
const props = defineProps({ id: String });
const emit = defineEmits(["success", "close"]);
const formRef = shallowRef<FormInstance>();
const popupRef = shallowRef<InstanceType<typeof Popup>>();
const mode = ref("add");
const popupTitle = computed(() => {
  return mode.value == "edit" ? "編集" : "新規追加";
});

const formData: any = reactive({
  console_id: "",
  console_name: "",
  console_type: 1,
  console_sn: "",
  console_address: "",
  install_date: null,
  delivery_date: null,
  warranty_end_date: null,
});

const formRules = reactive({
  console_console_name: [
    {
      required: true,
      message: "例：操作台001",
      trigger: ["blur"],
    },
  ],
});

const consoleTypeList: any = ref([]);

onMounted(() => {
  // getConsoleTypeList();
});

const getConsoleTypeList = async () => {
  consoleTypeList.value = await metadataStore.fetchMetadata("console_type");
};

const handleSubmit = async () => {
  await formRef.value?.validate();
  mode.value == "edit"
    ? await projectEditConsole({ id: props.id, ...formData })
    : await projectAddConsole({ id: props.id, ...formData });
  popupRef.value?.close();
  feedback.msgSuccess("ok");
  emit("success");
};

const open = (type = "add") => {
  mode.value = type;
  popupRef.value?.open();
};

const setFormData = async (data: any) => {
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key];
    }
  }
};

const handleClose = () => {
  emit("close");
};

defineExpose({
  open,
  setFormData,
});
</script>

<style scoped lang="scss"></style>
