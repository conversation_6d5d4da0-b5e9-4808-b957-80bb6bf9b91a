<template>
  <div class="vehicle-card">
    <div class="flex justify-end items-center">
      <el-tooltip effect="light" placement="top" content="編集">
        <Icon class="w-6 card-icon" :size="16" name="el-icon-Edit" @click="consoleEdit" />
      </el-tooltip>
      <el-tooltip effect="light" placement="top" content="削除">
        <Icon class="w-6 card-icon" :size="16" name="el-icon-Delete" @click="consoleDelete" />
      </el-tooltip>
    </div>
    <div class="mx-auto my-2"><img :src="consoleImg" alt="" class="h-[80px]" /></div>
    <div class="vehicle-info">
      <div class="label">操作台番号:</div>
      <div class="value">{{ console_name || "--" }}</div>
    </div>
    <!-- <div class="vehicle-info">
      <div class="label">操作台类型:</div>
      <div class="value">{{ console_type || "--" }}</div>
    </div> -->
    <div class="vehicle-info">
      <div class="label">SN番号:</div>
      <div class="value">{{ console_sn || "--" }}</div>
    </div>
    <div class="vehicle-info">
      <div class="label">所在地:</div>
      <div class="value one-line-ellipsis">{{ console_address || "--" }}</div>
    </div>
    <div class="vehicle-info">
      <div class="label">取り付け日:</div>
      <div class="value">{{ install_date ? dayjs(install_date).format('YYYY-MM-DD') : "--" }}</div>
    </div>
    <div class="vehicle-info">
      <div class="label">引き渡し日:</div>
      <div class="value">{{ delivery_date ? dayjs(delivery_date).format('YYYY-MM-DD') : "--" }}</div>
    </div>
    <div class="vehicle-info">
      <div class="label">保証終了日:</div>
      <div class="value">{{ warranty_end_date ? dayjs(warranty_end_date).format('YYYY-MM-DD') : "--" }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import consoleImg from "@/assets/images/project/caozuotai.png";
import dayjs from "dayjs";

const props = defineProps({
  console_id: {
    type: String,
    default: "",
  },
  console_name: {
    type: String,
    default: "",
  },
  console_type: {
    type: Number,
    default: 1,
  },
  console_sn: {
    type: String,
    default: "",
  },
  console_address: {
    type: String,
    default: "",
  },
  install_date: {
    type: String,
    default: "",
  },
  delivery_date: {
    type: String,
    default: "",
  },
  warranty_end_date: {
    type: String,
    default: "",
  },
});

const { console_id, console_name, console_type, console_sn,console_address, install_date, delivery_date, warranty_end_date } =
  toRefs(props);

const emit = defineEmits(["edit", "copy", "delete"]);

const consoleEdit = () => {
  emit("edit", console_id.value);
};

const consoleDelete = () => {
  emit("delete", console_id.value);
};

onMounted(() => {});
</script>
<style scoped lang="scss">
img {
  user-select: none; /* 防止用户选择图片 */
  pointer-events: none; /* 防止图片被点击或拖动 */
}

.vehicle-card {
  padding: 8px;
  width: 190px;
  height: 100%;
  background-color: #f4f4f4;
  border-radius: 8px;
  margin-right: 8px;
  display: flex;
  flex-direction: column;
  .vehicle-info {
    height: 26px;
    line-height: 26px;
    display: flex;
    flex-direction: row;
    .label {
      width: 86px;
      color: #222831;
      word-break: keep-all;
    }
    .value {
      color: #393e46;
    }
  }
}
.card-icon {
  cursor: pointer;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 22px;
  height: 22px;
  border-radius: 4px;
  background-color: #bbb;
  transition: all 0.3s linear;

  &:hover {
    background-color: var(--el-color-primary);
    transform: scale(1.1);
  }
}

.card-icon + .card-icon {
  margin-left: 4px;
}
</style>
