<template>
  <div class="edit-popup">
    <popup
      ref="popupRef"
      :title="popupTitle"
      :async="true"
      width="550px"
      @confirm="handleSubmit"
      @close="handleClose"
      :confirmButtonText="$t('stream.确定')"
      :cancelButtonText="$t('stream.取消')"
    >
      <el-form ref="formRef" :model="formData" label-width="90px" :rules="formRules">
        <el-form-item label="お客様名" prop="customer_name">
          <el-select
            v-model="formData.customer_name"
            :disabled="mode !== 'add'"
            placeholder="顧客名を選択してください"
            clearable
            @change="handleCustomerSelect"
          >
            <el-option
              v-for="item in prjList"
              :key="item.id"
              :label="item.customer_name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="担当者" prop="person_name">
          <el-input
            v-model="formData.person_name"
            :disabled="mode !== 'add'"
            placeholder="联系人を入力してください"
            clearable
          />
        </el-form-item>
        <el-form-item label="連絡先" prop="contact_info">
          <el-input
            v-model="formData.contact_info"
            :disabled="mode !== 'add'"
            placeholder="連絡先を入力してください"
            clearable
          />
        </el-form-item>

        <el-form-item label="操作台" prop="console">
          <el-select
            v-model="formData.device_control_name"
            :disabled="mode !== 'add'"
            placeholder="操作台モデルとSN番号を入力してください"
            clearable
          >
            <el-option
              v-for="item in console_list"
              :key="item.console_name"
              :label="item.console_name"
              :value="item.console_name"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="重機側" prop="vehicle">
          <el-select
            v-model="formData.device_vehicle_name"
            :disabled="mode !== 'add'"
            placeholder="重機モデルとSN番号を入力してください"
            clearable
          >
            <el-option
              v-for="item in vehicle"
              :key="item.vehicle_name"
              :label="item.vehicle_name"
              :value="item.vehicle_name"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="mode == 'detail'"
          v-for="(item, index) in pageData.phenomenonText"
          :label="'現象' + (index + 1)"
          prop="phenomenon"
        >
          <el-input
            disabled
            v-model="item.content"
            placeholder="故障現象の補足があれば、ご入力お願いいたします"
            clearable
          />
        </el-form-item>

        <el-form-item
          v-if="formData.phenomenon_description && mode == 'detail'"
          label="現象説明"
          prop="phenomenon_description"
        >
          <el-input
            v-model="formData.phenomenon_description"
            disabled
            type="textarea"
            :rows="3"
            placeholder="現象の説明を入力してください"
            clearable
          />
        </el-form-item>

        <el-form-item label="備考" prop="status_remark">
          <el-input
            v-model="formData.status_remark"
            type="textarea"
            :rows="3"
            placeholder="備考に任意入力してください"
            clearable
          />
        </el-form-item>

        <el-form-item label="ファイル説明" prop="imageUpload">
          <upload
            class="mr-3"
            v-model="formData.imgUrlList"
            :data="{ cid: 0 }"
            type="*"
            :show-progress="true"
            listType="picture-card"
            :showFileList="true"
            @success="handleFileSuccess"
          >
            <template #miniImg="{ file }">
              <div>
                <!-- 根据文件类型显示不同的预览 -->
                <div v-if="isImageFile(file.name)" class="file-preview-container">
                  <img class="el-upload-list__item-thumbnail" :src="file.url" alt="error" />
                </div>
                <div v-else class="file-preview-container file-icon-container">
                  <Icon
                    :color="getFileTypeColor(file.name)"
                    :size="42"
                    :name="getFileTypeIcon(file.name)"
                  />
                  <div class="file-name">{{ getFileName(file.name) }}</div>
                </div>

                <span class="el-upload-list__item-actions">
                  <span
                    class="el-upload-list__item-preview"
                    @click="handleFilePreview(file)"
                    :title="isVideoFile(file.name) ? '视频预览' : '文件预览'"
                  >
                    <el-icon>
                      <zoom-in v-if="isImageFile(file.name)" />
                      <video-play v-else-if="isVideoFile(file.name)" />
                      <view v-else />
                    </el-icon>
                  </span>
                  <span
                    v-if="!disabled"
                    class="el-upload-list__item-delete"
                    @click="handleDownload(file)"
                    title="下载文件"
                  >
                    <el-icon><Download /></el-icon>
                  </span>
                  <span
                    v-if="!disabled"
                    class="el-upload-list__item-delete"
                    @click="handleRemove(file)"
                    title="删除文件"
                  >
                    <el-icon><Delete /></el-icon>
                  </span>
                </span>
              </div>
            </template>
          </upload>
        </el-form-item>

        <!-- <el-form-item
          v-if="mode !== 'add' && formData.imgUrlList.length > 0"
          label="图片描述"
          prop="phenomenon"
        >
          <div class="demo-image__preview" style="display: flex; flex-wrap: wrap">
            <el-image
              v-for="(url, index) in formData.imgUrlList"
              :key="url"
              style="width: 100px; height: 100px; margin-right: 10px; margin-bottom: 10px"
              :src="url"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="formData.imgUrlList"
              :initial-index="index"
              fit="cover"
              show-progress
            />
          </div>
        </el-form-item> -->
        <el-form-item v-if="mode == 'feedback' || mode == 'feedback'" label="現象説明" prop="phone">
          <el-input
            type="textarea"
            v-model="formData.phenomenon_description"
            placeholder="現象の説明を入力してください"
            clearable
          />
        </el-form-item>

        <el-form-item v-if="mode == 'resolve'" label="対応策" prop="resolve">
          <el-select v-model="formData.resolve" placeholder="解決策を選択してください" clearable>
            <el-option
              v-for="item in operation_arr"
              :key="item.content"
              :label="item.content"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="mode == 'detail'" label="対応策" prop="resolve">
          <el-input disabled v-model="pageData.resolveText" placeholder="対応策" clearable />
        </el-form-item>

        <el-form-item v-if="mode == 'resolve'" label="他の方法で解決" prop="resolve">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="解決方法を入力してください"
            clearable
          />
        </el-form-item>

        <el-form-item v-if="formData.remark && mode == 'detail'" label="備考" prop="remark">
          <el-input
            disabled
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="備考に任意入力してください"
            clearable
          />
        </el-form-item>

        <el-form-item v-if="mode == 'detail'" label="タイムライン " prop="timeline">
          <el-timeline style="max-width: 390px">
            <el-timeline-item
              v-for="(activity, index) in formData.timeline"
              :key="index"
              :timestamp="dayjs(activity.timestamp).local().format('YYYY-MM-DD HH:mm:ss')"
            >
              {{ activity.content }}
            </el-timeline-item>
          </el-timeline>
        </el-form-item>
        <span v-if="mode == 'feedback'" class="text-primary-light-3 ml-3"
          >緊急の場合は4001-133-112までご連絡ください。</span
        >
      </el-form>
    </popup>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="dialogVisible" title="图片预览" width="60%">
      <img w-full :src="dialogImageUrl" alt="Preview Image" style="max-width: 100%; height: auto" />
    </el-dialog>

    <!-- 视频预览对话框 -->
    <el-dialog v-model="videoDialogVisible" title="视频预览" width="70%">
      <video-player
        v-if="videoDialogVisible && dialogVideoUrl"
        :src="dialogVideoUrl"
        width="100%"
        height="400px"
      />
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { Delete, Download, Plus, ZoomIn, VideoPlay, View } from "@element-plus/icons-vue";
import type { UploadFile } from "element-plus";
import type { FormInstance } from "element-plus";
import feedback from "@/utils/feedback";
import Popup from "@/components/popup/index.vue";
import VideoPlayer from "@/components/video-player/index.vue";
import { orderAdd, projectList, orderEdit } from "@/api/customer-ja";
import { storeToRefs } from "pinia";
import useCustomerJaStore from "@/stores/modules/customer-ja";
import {
  getFileTypeByExtension,
  isImageFile,
  isVideoFile,
  isAudioFile,
  getFileTypeIcon,
  getFileTypeColor,
} from "@/utils/fileType";
import { downloadFile } from "@/utils/download";
const customerStore = useCustomerJaStore();
const { phenomenonList, operationList } = storeToRefs(customerStore);

const props = defineProps({
  operation_arr: {
    type: Array as any,
    require: true,
  },
});

const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const dialogVideoUrl = ref("");
const videoDialogVisible = ref(false);
const disabled = ref(false);

const handleRemove = (file: UploadFile) => {
  formData.imgUrlList = formData.imgUrlList.filter((item: any) => item.name != file.name);
};

const handleFilePreview = (file: UploadFile) => {
  if (isImageFile(file.name)) {
    // 图片预览
    dialogImageUrl.value = file.url!;
    dialogVisible.value = true;
  } else if (isVideoFile(file.name)) {
    // 视频预览
    dialogVideoUrl.value = file.url!;
    videoDialogVisible.value = true;
  } else {
    // 其他文件类型，提示下载
    feedback.msgWarning("该文件类型不支持预览，请下载后查看");
    handleDownload(file);
  }
};

const handleDownload = async (file: UploadFile) => {
  if (!file.url) {
    feedback.msgError("error");
    return;
  }

  // 使用统一的下载工具函数
  await downloadFile(file.url, file.name || "download");
};



// 获取文件名（去掉路径）
const getFileName = (filename: string) => {
  if (!filename) return "";
  return filename.split("/").pop() || filename;
};

const handleFileSuccess = (file: any, nameUrlList: any) => {
  formData.imgUrlList = nameUrlList;
};

const emit = defineEmits(["success", "close", "add", "resolve", "feedback", "edit"]);
const formRef = shallowRef<FormInstance>();
const popupRef = shallowRef<InstanceType<typeof Popup>>();

const mode = ref("add");

const popupTitle = computed(() => {
  if (mode.value == "detail") {
    return "ケース詳細";
  } else if (mode.value == "add") {
    return "新規作成";
  } else if (mode.value == "feedback") {
    return "BuilderXへ";
  } else if (mode.value == "resolve") {
    return "解決完了";
  } else if (mode.value == "edit") {
    return "編集";
  }
});

const pageData: any = reactive({
  resolveText: "",
  phenomenonText: [],
});

const formData: any = reactive({
  id: "",
  customer_id: "",
  customer_name: "",
  contact_info: "",
  create_username: "",
  person_name: "",
  device_control_name: "",
  device_vehicle_name: "",
  phenomenon_description: "",
  status: 1,
  phenomenon_id: [],
  operation_id: [],
  operation_checked: [],
  resolve: "",
  remark: "",
  status_remark: "",
  timeline: [],
  imgUrlList: [],
});

const prjList = ref([
  {
    id: "",
    person_name: "",
    customer_name: "",
    contact_info: "",
    console: [{ console_name: "" }],
    vehicle: [{ vehicle_name: "" }],
  },
]);
const console_list = ref([{ console_name: "" }]);

const vehicle = ref([{ vehicle_name: "" }]);

const formRules = reactive({
  customer_name: [
    {
      required: true,
      message: "入力してください",
      trigger: ["blur"],
    },
  ],
  contact_info: [
    {
      required: false,
      message: "入力してください",
      trigger: ["blur"],
    },
  ],
});

const handleCustomerSelect = (value: string) => {
  const selectedPrj = prjList.value.find(({ id }) => id === value);
  if (selectedPrj) {
    Object.assign(formData, {
      customer_id: selectedPrj.id,
      person_name: selectedPrj.person_name,
      customer_name: selectedPrj.customer_name,
      contact_info: selectedPrj.contact_info,
    });

    // 获取设备列表
    if (selectedPrj.console.length > 0) {
      console_list.value = selectedPrj.console;
      formData.device_control_name = selectedPrj.console[0].console_name;
    }
    if (selectedPrj.vehicle.length > 0) {
      vehicle.value = selectedPrj.vehicle;
      formData.device_vehicle_name = selectedPrj.vehicle[0].vehicle_name;
    }
  }
};

const handleSubmit = async () => {
  await formRef.value?.validate();
  feedback.loading("");
  if (mode.value == "add") {
    const row = await orderAdd(formData);
    emit("add", row);
    feedback.msgSuccess("操作が成功しました");
  } else if (mode.value == "detail") {
  } else if (mode.value == "feedback") {
    formData.status = 4;
    const { content } = await orderEdit(formData);
    feedback.msgSuccess("ご連絡を受け取りました。早急にご対応いたします。");
    emit("feedback", content);
  } else if (mode.value == "resolve") {
    if (!formData.resolve && !formData.remark) {
      return feedback.msgWarning("解決策を選択または入力してください");
    }
    formData.status = 3;
    await orderEdit(formData);
    await feedback.msgSuccess("ご対応ありがとうございます");
    emit("resolve");
  } else if (mode.value == "edit") {
    const { content } = await orderEdit(formData);
    emit("edit", content);
    feedback.msgSuccess("操作が成功しました");
  }
  feedback.closeLoading();
  handleClose();
};

const open = async (type = "add") => {
  const { lists } = await projectList({});
  prjList.value = lists;
  mode.value = type;
  popupRef.value?.open();

  if (type == "detail") {
    pageData.phenomenonText = [];
    phenomenonList.value.forEach((item: any) => {
      item.children.forEach((child: any) => {
        if (formData.phenomenon_id.includes(child.id)) {
          pageData.phenomenonText.push({ content: child.content });
        }
      });
    });

    operationList.value.forEach((item: any) => {
      if (formData.resolve === item.id) {
        pageData.resolveText = item.content;
      }
    });
  }
};

const setFormData = async (row: any) => {
  const data = row;
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key];
    }
  }
};

const handleClose = () => {
  formData.prj_name = "";
  formData.customer_name = "";
  formData.contact_info = "";
  emit("close");
};

defineExpose({
  open,
  setFormData,
});
</script>

<style scoped lang="scss">
.file-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.file-icon-container {
  flex-direction: column;
  background-color: #f5f7fa;
  border-radius: 6px;
  padding: 8px;

  .file-name {
    font-size: 12px;
    color: #606266;
    text-align: center;
    margin-top: 4px;
    word-break: break-all;
    line-height: 1.2;
    max-height: 24px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}

.el-upload-list__item-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

// 覆盖 Element Plus 的样式
:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 148px;
  height: 148px;
}

:deep(.el-upload-list__item-actions) {
  .el-upload-list__item-preview,
  .el-upload-list__item-delete {
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      color: var(--el-color-primary);
    }
  }
}
</style>
