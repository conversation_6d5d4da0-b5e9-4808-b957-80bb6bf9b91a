<template>
  <div class="menu-lists">
    <el-card class="!border-none" shadow="never">
      <div class="flex item-center">
        <el-button type="primary" @click="handleAdd()">
          <template #icon>
            <icon name="el-icon-Plus" />
          </template>
          新增
        </el-button>
        <el-button type="primary" @click="syncData()"> 同步飞书表格数据 </el-button>
        <el-button @click="handleExpand">展开/折叠</el-button>
      </div>

      <el-table
        v-loading="loading"
        ref="tableRef"
        class="mt-4"
        size="large"
        :data="lists"
        row-key="id"
        :tree-props="{ children: 'children' }"
      >
        <el-table-column label="ID" prop="id" min-width="100" />
        <el-table-column label="现象" prop="content" min-width="150" show-overflow-tooltip />
        <el-table-column label="描述" prop="description" min-width="100" show-overflow-tooltip />
        <el-table-column label="排序" prop="sort" min-width="60"></el-table-column>
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleAdd(row.id)"> 新增 </el-button>
            <el-button type="primary" link @click="handleEdit(row)"> 编辑 </el-button>
            <el-button type="danger" link @click="handleDelete(row.id)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <EditPopup v-if="showEdit" ref="editRef" @success="getLists" @close="showEdit = false" />
  </div>
</template>
<script lang="ts" setup name="menu">
import type { ElTable } from "element-plus";
import EditPopup from "./edit.vue";
import feedback from "@/utils/feedback";
import { phenomenonList, phenomenonAdd, phenomenonDelete, syncDataApi } from "@/api/customer-ja";

const tableRef = shallowRef<InstanceType<typeof ElTable>>();
const editRef = shallowRef<InstanceType<typeof EditPopup>>();
let isExpand = false;
const loading = ref(false);
const showEdit = ref(false);
const lists = ref([]);

const pageData: any = reactive({
  jsonFile: [],
});

const syncData = async () => {
  feedback.loading("正在同步数据");
  await syncDataApi();
  feedback.closeLoading();
  feedback.msgSuccess("同步成功");
  getLists();
};

const getLists = async () => {
  loading.value = true;
  try {
    const data = await phenomenonList({});
    lists.value = data.lists;
    loading.value = false;
  } catch (error) {
    loading.value = false;
  }
};

const handleAdd = async (id?: number) => {
  showEdit.value = true;
  await nextTick();
  if (id) {
    editRef.value?.setFormData({
      pid: id,
    });
  }
  editRef.value?.open("add");
};

const handleEdit = async (data: any) => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("edit");
  editRef.value?.setFormData(data);
};

const handleDelete = async (id_: number) => {
  await feedback.confirm("本当に削除してよろしいでしょうか？");
  await phenomenonDelete(id_);
  feedback.msgSuccess("削除に成功しました");
  getLists();
};

const handleExpand = () => {
  isExpand = !isExpand;
  toggleExpand(lists.value, isExpand);
};

const toggleExpand = (children: any[], unfold = true) => {
  for (const key in children) {
    tableRef.value?.toggleRowExpansion(children[key], unfold);
    if (children[key].children) {
      toggleExpand(children[key].children!, unfold);
    }
  }
};

getLists();
</script>
