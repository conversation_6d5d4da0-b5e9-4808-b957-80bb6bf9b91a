<template>
  <el-card class="!border-none" shadow="never">
    <el-form ref="formRef" class="mb-[-16px]" :model="queryParams" :inline="true">
      <el-form-item class="w-[240px]" label="设备选择">
        <el-select v-model="queryParams.deviceId" placeholder="请选择设备">
          <el-option
            v-for="item in deviceData"
            :key="item.device_id"
            :label="item.device_name"
            :value="item.device_id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="时间">
        <el-date-picker
          class="grow-0 !w-[330px]"
          v-model="queryParams.dateValue"
          type="datetimerange"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>

    </el-form>
  </el-card>
  <el-card class="!border-none mt-4" shadow="never">

  </el-card>
</template>

<script setup lang="ts">
import { usePaging } from "@/hooks/usePaging";
import feedback from "@/utils/feedback";

const calcTableHeight = () => {
  return window.innerHeight - 228;
};

const queryParams = reactive({
  deviceId: "",
  dateValue: "",
});
type DeviceData = {
  device_id: string;
  device_name: string;
};
const deviceData = ref<DeviceData[]>([]);

</script>

<style scoped lang="scss"></style>
