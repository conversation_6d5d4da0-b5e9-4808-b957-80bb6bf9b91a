<template>
  <div class="material-index">
    <el-card class="!border-none" shadow="never">
      <material
        v-if="isShow"
        style="height: calc(100vh - 126px)"
        type="video"
        file-size="120px"
        :limit="-1"
        :page-size="20"
      />
    </el-card>
  </div>
</template>

<script lang="ts" setup name="materialCenter">
import type { LocaleType } from "@/locales/lang";
import { useLocale } from "@/locales/useLocale";
import useUserStore from "@/stores/modules/user";

const userStore = useUserStore();
const { changeLocale } = useLocale();
const route = useRoute();

let { lang } = route.query;
const isShow = ref(true);

onBeforeMount(async () => {
  if (route.path.includes("vid")) {
    isShow.value = false;
    let res: any = await userStore.login({
      account: "stall",
      password: "builderx",
    });
    if (res.token) isShow.value = true;
  }
});

onMounted(async () => {
  if (lang) changeLocale(lang as LocaleType);
});
</script>

<style lang="scss" scoped>
.material-index {
  min-width: 700px;
}
</style>
