<template>
  <div class="department-lists">
    <el-card class="!border-none" shadow="never">
      <div>
        <el-button type="primary" @click="handleAdd">
          <template #icon>
            <icon name="el-icon-Plus" />
          </template>
          新增
        </el-button>
      </div>
      <div class="mt-4">
        <div class="mb-4 flex items-center">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入部门名称"
            clearable
            class="!w-240px mr-2"
            @keyup.enter="getLists"
          />
          <el-select
            v-model="queryParams.is_enable"
            placeholder="状态"
            clearable
            class="!w-120px mr-2"
          >
            <el-option label="正常" :value="0" />
            <el-option label="停用" :value="1" />
          </el-select>
          <el-button type="primary" @click="getLists">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </div>
        <div>
          <el-table
            :data="pager.lists"
            size="large"
            v-loading="pager.loading"
            row-key="id"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          >
            <el-table-column prop="name" label="部门名称" min-width="150" />
            <el-table-column prop="sort" label="排序" min-width="100" />
            <el-table-column prop="is_enable" label="状态" min-width="100">
              <template #default="{ row }">
                <el-tag :type="row.is_enable === 0 ? 'success' : 'danger'">
                  {{ row.is_enable === 0 ? "正常" : "停用" }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
            <el-table-column prop="create_time" label="创建时间" min-width="180">
              <template #default="{ row }">
                {{ dayjs(row.create_time).local().format("YYYY-MM-DD HH:mm:ss") }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="240" fixed="right">
              <template #default="{ row }">
                <el-button link type="primary" @click="handleEdit(row)"> 编辑 </el-button>
                <el-button link type="primary" @click="handleAddChild(row)"> 增加下级部门 </el-button>
                <el-button link type="danger" @click="handleDelete(row.id)"> 删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
    <edit-popup v-if="showEdit" ref="editRef" @success="getLists" @close="showEdit = false" />
  </div>
</template>

<script lang="ts" setup name="department">
import { departmentTree, departmentDelete } from "@/api/perms/department";
import { usePaging } from "@/hooks/usePaging";
import feedback from "@/utils/feedback";
import EditPopup from "./edit.vue";

const editRef = shallowRef<InstanceType<typeof EditPopup>>();
const showEdit = ref(false);

const queryParams = reactive({
  name: "",
  is_enable: undefined,
});

const { pager, getLists } = usePaging({
  fetchFun: departmentTree,
  params: queryParams,
});

const resetQuery = () => {
  queryParams.name = "";
  queryParams.is_enable = undefined;
  getLists();
};

const handleAdd = async () => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("add");
};

const handleEdit = async (data: any) => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("edit");
  editRef.value?.setFormData(data);
};

const handleAddChild = async (data: any) => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("add");
  console.log(data);
  
  // 设置父部门ID为当前部门ID
  editRef.value?.setParentId(data.id);
};

const handleDelete = async (id: string) => {
  await feedback.confirm("确定要删除该部门吗？");
  await departmentDelete(id);
  feedback.msgSuccess("删除成功");
  getLists();
};

onMounted(() => {
  getLists();
});
</script>
