<template>
  <div class="menu-lists">
    <el-card class="!border-none" shadow="never">
      <div class="flex item-center">
        <el-button type="primary" @click="handleAdd()">
          <template #icon>
            <icon name="el-icon-Plus" />
          </template>
          新增
        </el-button>
        <el-button @click="handleExpand"> 展开/折叠 </el-button>
        <el-button type="default" @click="downMenu()">
          <template #icon> <icon name="local-icon-xiazai" /> </template> 下载当前菜单
        </el-button>

        <el-upload
          class="upload-demo inline-block ml-3"
          :auto-upload="false"
          :limit="99"
          :on-change="handleFileChange"
          :show-file-list="false"
          accept=".json"
        >
          <template #trigger>
            <el-button type="default">
              <template #icon> <icon name="local-icon-shangchuan" /> </template> 上传菜单
            </el-button>
          </template>
        </el-upload>
      </div>

      <el-table
        v-loading="loading"
        ref="tableRef"
        class="mt-4"
        size="large"
        :data="lists"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column label="菜单名称" prop="menuName" min-width="150" show-overflow-tooltip />
        <el-table-column label="类型" prop="menuType" min-width="80">
          <template #default="{ row }">
            <div v-if="row.menuType == MenuEnum.CATALOGUE">目录</div>
            <div v-else-if="row.menuType == MenuEnum.MENU">菜单</div>
            <div v-else-if="row.menuType == MenuEnum.BUTTON">按钮</div>
          </template>
        </el-table-column>
        <el-table-column label="图标" prop="menuIcon" min-width="80">
          <template #default="{ row }">
            <div class="flex">
              <icon :name="row.menuIcon" :size="20" />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="权限标识" prop="perms" min-width="150" show-overflow-tooltip />
        <el-table-column label="状态" prop="isDisable" min-width="100">
          <template #default="{ row }">
            <el-tag v-if="row.isDisable == 0">正常</el-tag>
            <el-tag v-else type="danger">停用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="排序" prop="menuSort" min-width="100" />
        <el-table-column label="更新时间" prop="updateTime" min-width="180">
          <template #default="{ row }">
            {{ dayjs(row.updateTime).local().format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleAdd(row.id)"> 新增 </el-button>
            <el-button type="primary" link @click="handleEdit(row)"> 编辑 </el-button>
            <el-button type="danger" link @click="handleDelete(row.id)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <EditPopup v-if="showEdit" ref="editRef" @success="getLists" @close="showEdit = false" />
    <EditParams ref="editParamsRef" :menuJson="pageData.jsonFile" @success="getLists" />
  </div>
</template>
<script lang="ts" setup name="menu">
import { menuDelete, menuLists, menuDownload } from "@/api/perms/menu";
import type { ElTable } from "element-plus";
import { MenuEnum } from "@/enums/appEnums";
import EditPopup from "./edit.vue";
import EditParams from "./edit-params.vue";

import feedback from "@/utils/feedback";
import { downJsonFile, timeFormat, treeToArray } from "@/utils/util";
const tableRef = shallowRef<InstanceType<typeof ElTable>>();
const editRef = shallowRef<InstanceType<typeof EditPopup>>();
const editParamsRef = shallowRef<InstanceType<typeof EditParams>>();
let isExpand = false;
const loading = ref(false);
const showEdit = ref(false);
const lists = ref([]);

const pageData: any = reactive({
  jsonFile: [],
});

const getLists = async () => {
  loading.value = true;
  try {
    const { lists: data }: any = await menuLists();
    lists.value = data;
    loading.value = false;
  } catch (error) {
    loading.value = false;
  }
};

const handleAdd = async (id?: number) => {
  showEdit.value = true;
  await nextTick();
  if (id) {
    editRef.value?.setFormData({
      pid: id,
    });
  }
  editRef.value?.open("add");
};

const handleEdit = async (data: any) => {
  showEdit.value = true;
  await nextTick();
  editRef.value?.open("edit");
  editRef.value?.getDetail(data);
};

const handleDelete = async (id_: number) => {
  await feedback.confirm("确定要删除？");
  await menuDelete({ id_ });
  feedback.msgSuccess("删除成功");
  getLists();
};

const handleExpand = () => {
  isExpand = !isExpand;
  toggleExpand(lists.value, isExpand);
};

const toggleExpand = (children: any[], unfold = true) => {
  for (const key in children) {
    tableRef.value?.toggleRowExpansion(children[key], unfold);
    if (children[key].children) {
      toggleExpand(children[key].children!, unfold);
    }
  }
};

// 下载当前列表为json文件
const downMenu = async () => {
  const { lists: res } = await menuDownload();
  let fileName = `menu_${timeFormat(Date.now(), "mm月dd日hh时MM分")}.json`;
  downJsonFile(res, fileName);
};

const handleFileChange = async (file: any) => {
  if (file && file.raw) {
    const reader: any = new FileReader();

    reader.onload = async () => {
      try {
        pageData.jsonFile = treeToArray(JSON.parse(reader.result));

        setTimeout(() => {
          editParamsRef.value?.open();
        }, 200);
      } catch (error) {
        console.error("Invalid JSON file");
      }
    };
    reader.readAsText(file.raw);
  }
};

getLists();
</script>
