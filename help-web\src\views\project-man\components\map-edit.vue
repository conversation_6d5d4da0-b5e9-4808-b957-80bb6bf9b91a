<template>
  <popup
    custom-class="no-footer"
    ref="popupRef"
    title="坐标拾取"
    :async="false"
    width="70%"
    @confirm="handleSubmit"
    @close="handleClose"
  >
    <div id="container1"></div>
    <div class="content-wrap">
      <div class="flex items-center mt-2">
        <div class="w-[50px]">经度:</div>
        <el-input class="flex-1" v-model="pageData.lng" placeholder="请在地图上选择坐标" />
      </div>
      <div class="flex items-center mt-2">
        <div class="w-[50px]">纬度:</div>
        <el-input class="flex-1" v-model="pageData.lat" placeholder="请在地图上选择坐标" />
      </div>
      <div class="flex items-center mt-2">
        <div class="w-[50px]">地址:</div>
        <el-input class="flex-1" v-model="pageData.address" placeholder="请在地图上选择坐标" />
      </div>
    </div>
  </popup>
</template>

<script lang="ts" setup>
import AMapLoader from "@amap/amap-jsapi-loader";
import Popup from "@/components/popup/index.vue";
import feedback from "@/utils/feedback";

let currentMarker: any = null; // 在组件的data中定义一个变量来存储当前的marker
const pageData = reactive({
  lng: 0,
  lat: 0,
  address: "",
});

const emit = defineEmits(["success", "close"]);
const popupRef = shallowRef<InstanceType<typeof Popup>>();

const open = () => {
  popupRef.value?.open();
  initMap();
};

const handleClose = () => {
  emit("close");
};

const handleSubmit = () => {
  const params = {
    prj_position: [pageData.lng, pageData.lat],
    prj_address: pageData.address,
  };
  emit("success", params);
};

let map: any = null;

const handleMapClick = (e: any) => {
  pageData.lng = e.lnglat.getLng();
  pageData.lat = e.lnglat.getLat();
  if (currentMarker) {
    map.remove(currentMarker);
    currentMarker = null;
  }
  //@ts-ignore
  currentMarker = new AMap.Marker({
    icon: "https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png",
    position: [e.lnglat.getLng(), e.lnglat.getLat()],
    //@ts-ignore
    offset: new AMap.Pixel(-9, -30),
  });
  map.add(currentMarker);
  currentMarker.setPosition([e.lnglat.getLng(), e.lnglat.getLat()]);
  //@ts-ignore
  var geocoder = new AMap.Geocoder({
    city: "全国", //城市设为北京，默认：“全国”
    radius: 1000, //范围，默认：500
  });

  geocoder.getAddress([e.lnglat.getLng(), e.lnglat.getLat()], function (status: any, result: any) {
    if (status === "complete" && result.regeocode) {
      pageData.address = result.regeocode.formattedAddress;
    } else {
      console.log("根据经纬度查询地址失败");
    }
  });
};

const initMap = () => {
  AMapLoader.load({
    key: "f0f7e58b7882db63bffab5fa22f8611c", // 申请好的Web端开发者Key，首次调用 load 时必填
    version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
    plugins: [
      "AMap.Geolocation", // 定位控件，用来获取和展示用户主机所在的经纬度位置
      "AMap.Geocoder", // 逆地理编码,通过经纬度获取地址所在位置详细信息
    ],
  })
    .then((AMap) => {
      map = new AMap.Map("container1", {
        zoom: 13, // 初始化地图级别
        resizeEnable: true,
      });

      map.on("click", handleMapClick);
    })
    .catch((e) => {
      console.log(e);
    });
};

onMounted(() => {
  //@ts-ignore
  window._AMapSecurityConfig = {
    securityJsCode: "163aa7cfca1e67b5d6eb627b2e014b57", // "安全密钥",
  };
});

onUnmounted(() => {
  map?.destroy();
});

defineExpose({
  open,
});
</script>

<style scoped>
#container1 {
  width: 100%;
  height: 450px;
}
:deep(.el-dialog__footer) {
  padding-top: 0 !important;
}
</style>
