<template>
  <div class="vehicle-card">
    <div class="flex justify-end items-center">
      <el-tooltip effect="light" placement="top" content="复制车辆">
        <Icon class="w-6 card-icon" :size="16" name="el-icon-DocumentCopy" @click="vehicleCopy" />
      </el-tooltip>
      <el-tooltip effect="light" placement="top" content="编辑车辆">
        <Icon class="w-6 card-icon" :size="16" name="el-icon-Edit" @click="vehicleEdit" />
      </el-tooltip>
      <el-tooltip effect="light" placement="top" content="删除车辆">
        <Icon class="w-6 card-icon" :size="16" name="el-icon-Delete" @click="vehicleDelete" />
      </el-tooltip>
    </div>
    <div class="mx-auto">
      <img :src="typeImg[vehicle_type]" alt="" class="h-[80px]" />
    </div>
    <div class="vehicle-info">
      <div class="label">车辆名称:</div>
      <div class="value">{{ vehicle_name || "--" }}</div>
    </div>
    <div class="vehicle-info">
      <div class="label">车辆类型:</div>
      <div class="value">{{ typeEnum[vehicle_type] || "--" }}</div>
    </div>
    <div class="vehicle-info">
      <div class="label">S/N码:</div>
      <div class="value">{{ vehicle_sn || "--" }}</div>
    </div>
    <div class="vehicle-info">
      <div class="label">现场工程师:</div>
      <div class="value">{{ installer_name || "--" }}</div>
    </div>
    <div class="vehicle-info">
      <div class="label">安装进度:</div>
      <div class="value flex align-center">
        <el-progress
          class="w-[88px]"
          striped
          striped-flow
          :duration="10"
          :text-inside="true"
          :stroke-width="16"
          :percentage="completion_rate"
        />
      </div>
    </div>
    <div class="vehicle-info">
      <div class="label">当前节点:</div>
      <div class="value">{{ completion_rate || "--" }}</div>
    </div>
    <div class="vehicle-info">
      <div class="label h-[52px]">智能功能:</div>
      <div class="value h-[52px] w-[88px] two-line-ellipsis">
        <span v-for="(item, index) in smart_feature" :key="index">
          {{ smartFeatureList[item as number] }}
          <span v-if="index !== smart_feature.length - 1">、</span>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import excavatorImg from "@/assets/images/project/wajueji.gif";
import loaderImg from "@/assets/images/project/zhuangzaiji.gif";
import bulldozerImg from "@/assets/images/project/tuituji.gif";
import shovelImg from "@/assets/images/project/dianchan.gif";
import slingerImg from "@/assets/images/project/diaoyunji.gif";

import useMetadataStore from "@/stores/modules/metadata";
const metadataStore = useMetadataStore();

const typeImg: any = {
  1: excavatorImg,
  2: shovelImg,
  4: loaderImg,
  7: bulldozerImg,
  11: slingerImg,
};

const typeEnum: any = {
  1: "挖掘机",
  2: "电铲",
  4: "装载机",
  7: "推土机",
  11: "正面吊",
};

const props = defineProps({
  vehicle_id: {
    type: String,
    default: "",
  },
  vehicle_name: {
    type: String,
    default: "",
  },
  vehicle_type: {
    type: Number,
    default: 1,
  },
  vehicle_sn: {
    type: String,
    default: "",
  },
  installer: {
    type: Array,
    default: () => [],
  },
  completion_rate: {
    type: Number,
    default: 0,
  },
  smart_feature: {
    type: Array,
    default: () => [],
  },
  guide_timeline: {
    type: Array,
    default: () => [],
  },
});

const { vehicle_id, vehicle_name, vehicle_type, vehicle_sn, installer, completion_rate, guide_timeline } =
  toRefs(props);

const emit = defineEmits(["edit", "copy", "delete"]);

const installer_name = computed(() => {
  return installer.value.map((item: any) => item.nickname).join(",");
});

const vehicleEdit = () => {
  emit("edit", vehicle_id.value);
};

const vehicleCopy = () => {
  emit("copy", vehicle_id.value);
};

const vehicleDelete = () => {
  emit("delete", vehicle_id.value);
};

const smartFeatureList: any = ref({});
onMounted(async () => {
  smartFeatureList.value = await metadataStore.fetchMetadata("smart_feature", "kv");
});
</script>
<style scoped lang="scss">
img {
  user-select: none; /* 防止用户选择图片 */
  pointer-events: none; /* 防止图片被点击或拖动 */
}

.vehicle-card {
  padding: 8px;
  width: 190px;
  height: 100%;
  background-color: #f4f4f4;
  border-radius: 8px;
  margin-right: 8px;
  display: flex;
  flex-direction: column;
  .vehicle-info {
    height: 26px;
    line-height: 26px;
    display: flex;
    flex-direction: row;
    .label {
      width: 86px;
      color: #222831;
    }
    .value {
      color: #393e46;
    }
  }
}
.card-icon {
  cursor: pointer;
  color: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 22px;
  height: 22px;
  border-radius: 4px;
  background-color: #bbbbbb;
  transition: all 0.3s linear;

  &:hover {
    background-color: var(--el-color-primary);
    transform: scale(1.1);
  }
}

.card-icon + .card-icon {
  margin-left: 4px;
}

:deep(.el-progress-bar__outer) {
  background-color: #cccccc;
}
</style>
