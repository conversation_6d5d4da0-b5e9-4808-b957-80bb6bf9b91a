<template>
  <popup
    custom-class="no-footer"
    ref="popupRef"
    title="项目编辑"
    :async="false"
    width="70%"
    @confirm="handleSubmit"
    @close="handleClose"
  >
  
  </popup>
</template>

<script lang="ts" setup>
import Popup from "@/components/popup/index.vue";

const emit = defineEmits(["success", "close"]);
const popupRef = shallowRef<InstanceType<typeof Popup>>();

const open = () => {
  popupRef.value?.open();
};

const handleClose = () => {
  emit("close");
};

const handleSubmit = () => {
  emit("success");
  handleClose();
};

onMounted(() => {});

onUnmounted(() => {});

defineExpose({
  open,
});
</script>

<style scoped></style>
