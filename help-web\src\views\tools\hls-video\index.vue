<template>
  <div class="flex flex-row items-center my-5">
    <el-input v-model="pageData.url" placeholder="请输入HLS播放地址" size="default" clearable></el-input>
    <el-button class="ml-8" type="primary" size="default" @click="play()">播放</el-button>
  </div>

  <div id="app-container" class="video-box">
    <video ref="video" :src="pageData.url" controls muted style="width: 100%; height: 60vh; object-fit: fill"></video>
  </div>
</template>
<script lang="ts" setup>
import Hls from "hls.js"; // 从npm包中引入Hls.js

const pageData = reactive({
  url: "http://*************:8999/live/test_stream/index.m3u8",
});

let hls: any = null;
const video: any = ref(null);

const close = () => {
  if (hls) {
    hls.destroy();
    hls = null;
  }
};

const play = () => {
  close();
  if (Hls.isSupported()) {
    hls = new Hls();
    hls.loadSource(pageData.url);
    hls.attachMedia(video.value);
    hls.on(Hls.Events.MANIFEST_PARSED, () => {
      video.value.play();
    });
  } else if (video.value.canPlayType("application/vnd.apple.mpegurl")) {
    // 对于支持原生HLS的浏览器（如Safari）
    video.value.src = pageData.url;
    video.value.addEventListener("loadedmetadata", () => {
      video.value.play();
    });
  }
};

onMounted(() => {});

onBeforeUnmount(() => {
  close();
});
</script>
<style lang="scss" scoped></style>
