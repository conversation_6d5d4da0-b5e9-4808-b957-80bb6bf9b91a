<template>
  <div class="edit-popup">
    <popup ref="popupRef" :title="popupTitle" :async="true" width="550px" @confirm="handleSubmit" @close="handleClose">
      <el-form ref="formRef" :model="formData" label-width="84px" :rules="formRules">
        <el-form-item label="key" prop="key">
          <el-input v-model="formData.key" :placeholder="$t('vehicle.请输入')" clearable />
        </el-form-item>
        <el-form-item label="type" prop="type">
          <el-select v-model="formData.type" placeholder="请选择" clearable>
            <el-option v-for="item in FORM_TYPE_LIST" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="desc" prop="desc">
          <el-input v-model="formData.desc" :placeholder="$t('vehicle.请输入')" clearable />
        </el-form-item>
        <el-form-item label="value" prop="value">
          <el-input
            v-if="formData.type == 'string'"
            v-model="formData.value"
            :placeholder="$t('vehicle.请输入')"
            clearable
          />
          <el-input-number v-else-if="formData.type == 'number'" v-model="formData.value" controls-position="right" />
          <el-switch v-else-if="formData.type == 'boolean'" v-model="formData.value" @change="onBooleanSave" />
          <div v-else="formData.type == 'object' || formData.type == 'array'">
            <JsonEditorVue
              class="h-[400px] w-[390px]"
              v-model="params"
              mode="text"
              :mainMenuBar="false"
              :onChange="onJsonSave"
            />
          </div>
        </el-form-item>
      </el-form>
    </popup>
  </div>
</template>

<script setup lang="ts">
import { ElForm } from "element-plus";
import { metaDataList, metaDataEdit, metaDataAdd, metaDataDelete } from "@/api/setting/dict";

import JsonEditorVue from "json-editor-vue";
import Popup from "@/components/popup/index.vue";
import feedback from "@/utils/feedback";

const popupRef = shallowRef<InstanceType<typeof Popup>>();
const formRef = shallowRef<InstanceType<typeof ElForm>>();
const emit = defineEmits(["success", "close"]);

const mode = ref("add");
const popupTitle = computed(() => {
  return mode.value == "edit" ? "编辑" : "添加";
});

const formData: any = reactive({
  key: "",
  type: "string",
  desc: "",
  value: "",
});

const FORM_TYPE_LIST: any = [
  { label: "string", value: "string" },
  { label: "number", value: "number" },
  { label: "boolean", value: "boolean" },
  { label: "object", value: "object" },
  { label: "array", value: "array" },
];

const params: any = ref({});

const onBooleanSave = (value: any) => {
  console.log(value);
};

const onJsonSave = (value: any) => {
  formData.value = JSON.parse(value.text);
  console.log(formData.value);
};

const handleAddOption = () => {
  if (formData.value && formData.type == "object") {
    formData.key = "";
  }

  if (formData.value && formData.type == "array") {
    formData.value.push({ value: "" });
  }
};

const handleDeleteOption = (row: any) => {
  if (formData.type == "object") {
    formData.value.splice(formData.value.indexOf(row), 1);
  } else if (formData.type == "array") {
    formData.value.splice(formData.value.indexOf(row), 1);
  }
};

const handleSubmit = async () => {
  await formRef.value?.validate();
  mode.value == "edit" ? await metaDataEdit(formData) : await metaDataAdd(formData);
  popupRef.value?.close();
  feedback.msgSuccess("操作成功");
  emit("success");
};

const handleClose = () => {
  Object.assign(formData, {
    key: "",
    type: "string",
    desc: "",
    value: "",
  });
  emit("close");
};

const open = (type = "add") => {
  mode.value = type;
  popupRef.value?.open();
};

const setFormData = async (data: any) => {
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      formData[key] = data[key];
    }
  }

  if (data.type == "array" || data.type == "object") {
    formData.value = data.value;
    params.value = data.value;
  }
};

defineExpose({
  open,
  setFormData,
});
</script>
<style scoped lang="scss"></style>
