<template>
  <div class="post-lists">
    <!-- <el-card class="!border-none" shadow="never">
      <el-form ref="formRef" class="mb-[-16px]" :model="queryParams" :inline="true">
        <el-form-item :label="$t('vehicle.操作台名称')">
          <el-input class="w-[280px]" v-model="queryParams.name" clearable @keyup.enter="resetPage" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="resetPage">{{ $t("vehicle.查询") }}</el-button>
          <el-button @click="resetParams">{{ $t("vehicle.重置") }}</el-button>
        </el-form-item>
      </el-form>
    </el-card> -->
    <el-card class="!border-none mt-1 table-card" shadow="never">
      <div class="mb-4">
        <el-button type="primary" @click="handleAdd">
          <template #icon>
            <icon name="el-icon-Plus" />
          </template>
          添加元数据
        </el-button>
      </div>
      <div>
        <el-table :data="pageData.metaData">
          <el-table-column label="名称" prop="key"></el-table-column>
          <el-table-column label="值" prop="value"></el-table-column>
          <el-table-column label="类型" prop="type"></el-table-column>
          <el-table-column label="描述" prop="desc"></el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" link @click="handleShowDialog(row, 'edit')">编辑</el-button>
              <el-button type="primary" link @click="handleDeleteData(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="flex justify-end mt-4">
          <pagination v-model="pager" @change="getLists" />
        </div>
      </div>
    </el-card>

    <EditPopup
      v-if="pageData.dialogFormVisible"
      ref="editRef"
      @success="getMetaDataList"
      @close="pageData.dialogFormVisible = false"
    />
  </div>
</template>
<script lang="ts" setup>
import { metaDataList, metaDataEdit, metaDataAdd, metaDataDelete } from "@/api/setting/dict";
import feedback from "@/utils/feedback";
import EditPopup from "./edit.vue";

const editRef = shallowRef<InstanceType<typeof EditPopup>>();

const pageData: any = reactive({
  metaData: [],
  dialogFormVisible: false,
  dialogType: "add",
  curRow: {
    name: "",
    flag: 0,
    desc: "",
  },
});

const handleAdd = () => {
  console.log("handleAdd");

  handleShowDialog(
    {
      key: "",
      type: "string",
      desc: "",
      value: "",
    },
    "add"
  );
};

const getMetaDataList = async () => {
  const { lists } = await metaDataList();
  pageData.metaData = lists;
};

const handleShowDialog = async (row: any, type: string) => {
  pageData.dialogFormVisible = true;
  await nextTick();
  editRef.value?.open(type);
  if (type == "edit") {
    editRef.value?.setFormData(row);
  }
};

const handleDeleteData = async (row: any) => {
  await feedback.confirm("确定要删除？");
  await metaDataDelete(row.key);
  feedback.msgSuccess("删除成功");
  await getMetaDataList();
};

onMounted(() => {
  getMetaDataList();
});

defineExpose({
  handleShowDialog,
});
</script>
<style lang="scss" scoped></style>
