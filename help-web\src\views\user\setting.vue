<!-- 个人资料 -->
<template>
  <div class="user-setting">
    <el-card class="!border-none" shadow="never">
      <el-form ref="formRef" class="ls-form" :model="formData" :rules="rules" label-width="100px">
        <el-form-item :label="$t('common.账号')" prop="username">
          <div class="w-80">
            <el-input v-model="formData.username" disabled />
          </div>
        </el-form-item>

        <el-form-item :label="$t('common.名称')" prop="nickname">
          <div class="w-80">
            <el-input v-model="formData.nickname" :placeholder="$t('common.请输入名称')" />
          </div>
        </el-form-item>

        <el-form-item :label="$t('common.当前密码')" prop="oldPassword">
          <div class="w-80">
            <el-input
              v-model.trim="formData.oldPassword"
              :placeholder="$t('common.修改密码时必填')"
              type="password"
              show-password
            />
          </div>
        </el-form-item>

        <el-form-item :label="$t('common.新的密码')" prop="newPassword">
          <div class="w-80">
            <el-input
              v-model.trim="formData.newPassword"
              :placeholder="$t('common.修改密码时必填')"
              type="password"
              show-password
            />
          </div>
        </el-form-item>

        <el-form-item :label="$t('common.确定密码')" prop="passwordConfirm">
          <div class="w-80">
            <el-input
              v-model.trim="formData.passwordConfirm"
              :placeholder="$t('common.修改密码时必填')"
              type="password"
              show-password
            />
          </div>
        </el-form-item>
      </el-form>
    </el-card>
    <footer-btns>
      <el-button type="primary" @click="handleSubmit">保存</el-button>
    </footer-btns>
  </div>
</template>

<script setup lang="ts" name="userSetting">
import { setUserInfo, setUserPassword } from "@/api/user";
import useUserStore from "@/stores/modules/user";
import feedback from "@/utils/feedback";
import type { FormInstance } from "element-plus";
import { useI18n } from "vue-i18n";
const { t, locale } = useI18n();

const formRef = ref<FormInstance>();
const userStore = useUserStore();
// 表单数据
const formData = reactive({
  id: "", // id
  username: "", // 账号
  nickname: "", // 名称
  oldPassword: "", // 当前密码
  newPassword: "", // 新的密码
  passwordConfirm: "", // 确定密码
});

// 表单校验规则
const rules = reactive<object>({
  nickname: [
    {
      required: true,
      message: t("common.请输入名称"),
      trigger: ["blur"],
    },
  ],
  oldPassword: [
    {
      validator: (rule: object, value: string, callback: any) => {
        // if (formData.oldPassword) callback(new Error("请输入当前密码"));
        callback();
      },
      trigger: "blur",
    },
  ],
  newPassword: [
    {
      validator: (rule: object, value: string, callback: any) => {
        if (formData.oldPassword) {
          if (!formData.newPassword) callback(new Error(t("common.请输入新的密码")));
          if (formData.newPassword.length < 8) callback(new Error(t("common.密码长度不能小于8位")));
        }
        callback();
      },
      trigger: "blur",
    },
  ],
  passwordConfirm: [
    {
      validator: (rule: object, value: string, callback: any) => {
        if (formData.passwordConfirm !== formData.newPassword) callback(new Error(t("common.两次输入密码不一致!")));
        callback();
      },
      trigger: "blur",
    },
  ],
});

// 获取个人设置
const getUser = async () => {
  const userInfo = userStore.userInfo;
  for (const key in formData) {
    //@ts-ignore
    formData[key] = userInfo[key];
  }
};

// 设置个人设置
const setUser = async () => {
  if (formData.passwordConfirm) {
    await setUserPassword(formData);
  } else {
    await setUserInfo(formData);
  }
  userStore.logout();
  feedback.msgSuccess(t("common.操作成功"));
  userStore.getUserInfo();
};

// 提交数据
const handleSubmit = async () => {
  await formRef.value?.validate();
  setUser();
};

getUser();
</script>

<style lang="scss" scoped></style>
