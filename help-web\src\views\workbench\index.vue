<template>
  <div class="workbench pb-0">
    <div class="lg:flex bg-white mb-4 px-6 py-4">
      <el-avatar :src="avatar" :size="72" class="!mx-auto !block" />
      <div class="md:ml-6 flex flex-col justify-center md:mt-0 mt-2">
        <h1 class="md:text-xl text-xl mb-1">{{ pageData.greeting }}</h1>
        <span class="text-info"> {{ pageData.weatherMsg }} </span>
      </div>
      <div class="flex flex-1 justify-end md:mt-0 mt-4"></div>
    </div>
    <div class="function flex flex-row bg-white rounded-[4px] p-4">
      <div ref="chartRef" style="width: 100%; height: calc(100vh - 91px - 16px * 5 - 56px)"></div>
      <div v-if="hasInstallerPermission" class="w-[300px]">
        <div
          class="h-[80px] mb-2 flex flex-row justify-between items-center p-3 rounded-[4px]"
          v-for="(item, index) in pageData.productList"
          :style="'background-color: ' + colorList[index].color"
        >
          <div class="h-full w-[152px] flex flex-col justify-between">
            <div class="text-[18px] one-line-ellipsis">{{ item.prj_name }}</div>
            <div class="one-line-ellipsis">{{ item.prj_desc || "--" }}</div>
          </div>
          <div class="h-full button flex flex-col justify-between">
            <el-button class="" size="small" type="primary" @click="">更新进度</el-button>
            <el-button class="!ml-0" size="small" type="primary" @click="">创建工单</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="workbench">
import geoCoordMap from "./area.json";
import * as echarts from "echarts";

import avatar from "@/assets/images/avatar.png";
import device_zaixian from "./image/device_zaixian.png";
import device_lixian from "./image/device_lixian.png";
import device_error from "./image/device_error.png";

import { weatherForecast, workbenchProjectList } from "@/api/project-man";
import useUserStore from "@/stores/modules/user";
import { useI18n } from "vue-i18n";
const { t, locale } = useI18n();
const userStore = useUserStore();

const chartRef = ref<any>(null);
let chart: any = null;

const pageData = reactive({
  greeting: "",
  weatherMsg: "",
  productList: [
    {
      prj_name: "",
      prj_desc: "",
    },
  ],
});

const hasInstallerPermission = computed(() => {
  const permissions = userStore.perms;
  return permissions.some((key: string) => key === "project:limit");
});

const colorList = [
  {
    type: 0,
    desc: "未开始（灰色）",
    color: "rgb(232.8, 233.4, 234.6)",
  },
  {
    type: 1,
    desc: "已完成（绿色）",
    color: "rgb(209.4, 236.7, 195.9)",
  },
  {
    type: 2,
    desc: "进行中（黄色警告）",
    color: "rgb(255, 207, 179)",
  },
  {
    type: 3,
    desc: "保留（主题色）",
    color: "rgb(247.5, 227.1, 196.5)",
  },
  {
    type: 99,
    desc: "有工单（红色）",
    color: "rgb(252, 210.9, 210.9)",
  },
];

const getGreeting = () => {
  const date = new Date();
  const hour = date.getHours();
  let greeting = "早上好，拓疆者！愿你今天充满活力，心情愉快！";
  if (hour >= 6 && hour < 12) {
    if (locale.value === "zh_CN") {
      greeting = "早上好，拓疆者！愿你今天充满活力，心情愉快！";
    } else if (locale.value === "en") {
      greeting = "Good morning, trailblazer! May your day be filled with energy and joy!";
    } else if (locale.value === "ja") {
      greeting = "おはよう、開拓者さん！今日も元気で楽しい一日になりますように！";
    }
  } else if (hour >= 12 && hour < 14) {
    if (locale.value === "zh_CN") {
      greeting = "中午好，拓疆者！愿下午的工作继续顺利推进！";
    } else if (locale.value === "en") {
      greeting = "Good afternoon, trailblazer! May your afternoon's work continue smoothly!";
    } else if (locale.value === "ja") {
      greeting = "こんにちは、開拓者さん！午後の仕事も順調に進みますように！";
    }
  } else if (hour >= 14 && hour < 18) {
    if (locale.value === "zh_CN") {
      greeting = "下午好，拓疆者！愿你下午充满灵感与动力！";
    } else if (locale.value === "en") {
      greeting = "Good afternoon, trailblazer! May your afternoon be filled with inspiration and energy!";
    } else if (locale.value === "ja") {
      greeting = "こんにちは、開拓者さん！午後がインスピレーションと活力に満ちた時間になりますように！";
    }
  } else {
    if (locale.value === "zh_CN") {
      greeting = "晚上好，拓疆者！感谢一天的辛勤付出，辛苦了！";
    } else if (locale.value === "en") {
      greeting = "Good evening, trailblazer! Thank you for your hard work and hard work!";
    } else if (locale.value === "ja") {
      greeting = "こんばんは、開拓者さん！一日の努力を感謝します。";
    }
  }
  return greeting;
};

onMounted(async () => {
  await initEcharts();
  await initData();
});

onUnmounted(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener("resize", handleResize);
});

const handleResize = () => {
  chart.resize();
};

const changeOptions = (name = "china") => {
  // 经纬度数据
  const seriesList = [
    {
      icon: device_zaixian,
      name: "在线车辆",
      data: [
        {
          value: [86.13424378, 40.61847064],
          itemStyle: { color: "#41b75f" },
        },
        {
          value: [88.48134053, 31.77543918],
          itemStyle: { color: "#41b75f" },
        },
        {
          value: [96.94725647, 39.85060237],
          itemStyle: { color: "#41b75f" },
        },
        {
          value: [109.18522579, 41.4404807],
          itemStyle: { color: "#41b75f" },
        },
        {
          value: [126.70398738, 43.42002434],
          itemStyle: { color: "#41b75f" },
        },
        {
          value: [128.88337419, 47.01986805],
          itemStyle: { color: "#41b75f" },
        },
        {
          value: [115.72332742, 27.55253601],
          itemStyle: { color: "#41b75f" },
        },
        {
          value: [108.76613705, 33.9577419],
          itemStyle: { color: "#41b75f" },
        },
        {
          value: [140.7862289, 38.7544091],
          itemStyle: { color: "#41b75f" },
        },
      ],
    },
    {
      icon: device_lixian,
      name: "正在部署",
      data: [
        {
          value: [101.88372005, 24.18547614],
          itemStyle: { color: "rgb(243, 177, 83)" },
        },
        {
          value: [100.8799451, 31.10492549],
          itemStyle: { color: "rgb(243, 177, 83)" },
        },
        {
          value: [120.05664523, 28.90927923],
          itemStyle: { color: "rgb(243, 177, 83)" },
        },
        {
          value: [138.33965473, 35.88288268],
          itemStyle: { color: "rgb(243, 177, 83)" },
        },
      ],
    },
    {
      icon: device_error,
      name: "离线车辆",
      data: [
        {
          value: [120.84911051, 37.05070563],
          itemStyle: { color: "rgb(238, 80, 80)" },
        },
      ],
    },
  ];
  // 图标
  const series = seriesList.map((v) => {
    return {
      type: "scatter", //配置显示方式为用户自定义
      coordinateSystem: "geo",
      symbol: "image://" + v.icon,
      symbolSize: 20,
      data: v.data,
    };
  });

  // options
  let options = {
    tooltip: {
      // 提示框组件
      show: true, // 显示提示框组件
      trigger: "item", // 触发类型
      triggerOn: "mousemove", // 出发条件
      formatter: "名称:{b}<br/>坐标:{c}",
    },
    series: [], // 数据
    geo: {
      type: "map",
      map: "china", // 引入地图 省份或者 国家
      roam: false, //开启鼠标缩放和漫
      zoom: 1.23,
      label: {
        normal: {
          //静态的时候展示样式
          show: name === "world" ? false : true, //是否显示地图省份得名称
          textStyle: {
            color: "#000",
            fontSize: 8,
            fontFamily: "Arial",
          },
        },
        emphasis: {
          // 高亮状态下的样式
          //动态展示的样式
          show: name === "world" ? false : true,
          color: "#fff",
        },
      },
      itemStyle: {
        // 地图区域的多边形 图形样式。
        normal: {
          areaColor: "rgb(255, 239, 230)",
          borderWidth: 0.5, //设置外层边框
          shadowBlur: 1,
          shadowOffsetY: 1,
          shadowOffsetX: 0,
          // shadowColor: "#01012a",
          borderColor: "rgba(0, 0, 0, 0.1)",
        },
        emphasis: {
          areaColor: "rgb(255, 95, 0)",
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          shadowBlur: 5,
          borderWidth: 0,
          shadowColor: "rgba(0, 0, 0, 0.5)",
        },
      },
    },
    visualMap: {
      // 设置地图范围值显示的颜色
      selectedMode: false, // 不能选择
      hoverLink: false, // 取消鼠标移入效果
      textStyle: {
        color: "#86909C",
        fontSize: 12,
      },
      orient: "horizontal",
      itemGap: 10,
      itemWidth: 10,
      itemHeight: 10,
      pieces: [
        {
          gt: 0.9,
          lte: 1,
          label: "在线车辆",
          color: "#41B75F",
        },
        {
          gte: 0.8,
          lte: 0.9,
          label: "正在部署",
          color: "#F3B153",
        },
        {
          lt: 0.8,
          label: "离线车辆",
          color: "#EE5050",
        },
      ],
      top: 0,
      left: 0,
    },
  };

  chart.setOption(options);
};

const initData = async () => {
  pageData.greeting = getGreeting();
  const { msg } = await weatherForecast();
  pageData.weatherMsg = msg || "今日晴，20-25°C";
  window.addEventListener("resize", handleResize);
  const { lists } = await workbenchProjectList();
  pageData.productList = lists;
};

const initEcharts = async () => {
  chart = echarts.init(chartRef.value);
  echarts.registerMap("china", geoCoordMap as any);
  await changeOptions();
};
</script>

<style lang="scss" scoped></style>
